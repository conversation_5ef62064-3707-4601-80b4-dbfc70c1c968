'use client'

import dayjs from 'dayjs'
import React, { useEffect, useRef, useState } from 'react'
import {
    CreateTripEvent,
    UpdateTripEvent,
    CREATE_INFRINGEMENT_NOTICE,
    UPDATE_INFRINGEMENT_NOTICE,
} from '@/app/lib/graphQL/mutation'
import { GetTripEvent } from '@/app/lib/graphQL/query'
import Editor from '../../editor'
import { useLazyQuery, useMutation } from '@apollo/client'
import LocationField from '../components/location/location'
import TimeField from '../components/time'
import { UpdateInfringementNotice_Signature } from '@/app/lib/graphQL/mutation/logEntrySections/UpdateInfringementNotice_Signature'
import { CreateInfringementNotice_Signature } from '@/app/lib/graphQL/mutation/logEntrySections/CreateInfringementNotice_Signature'
import TripEventModel from '@/app/offline/models/tripEvent'
import InfringementNoticeModel from '@/app/offline/models/infringementNotice'
import InfringementNotice_SignatureModel from '@/app/offline/models/infringementNotice_Signature'
import { generateUniqueId } from '@/app/offline/helpers/functions'

// Shadcn UI components
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Combobox } from '@/components/ui/comboBox'
import { CheckFieldLabel } from '@/components/ui/check-field-label'
import { ArrowLeft, Check, X } from 'lucide-react'
import { toast } from 'sonner'
import DatePicker from '@/components/DateRange'
import { H3, H4, H5, P, Separator } from '@/components/ui'
import SignaturePad from '@/components/signature-pad'
import CloudFlareCaptures from '../components/CloudFlareCaptures'

export default function InfringementNotices({
    offline,
    currentTrip = false,
    updateTripReport,
    selectedEvent = false,
    tripReport,
    closeModal,
    crewMembers = [],
    locked,
    visibility = false,
}: {
    offline?: boolean
    currentTrip: any
    updateTripReport: any
    selectedEvent: any
    tripReport: any
    closeModal: any
    crewMembers: any
    locked: any
    visibility: boolean
}) {
    const currentEventRef = useRef<any>(null)
    const [time, setTime] = useState<any>()
    const [date, setDate] = useState<any>()
    const [content, setContent] = useState<any>('')
    const [infringementNotices, setInfringementNotices] = useState<any>(false)
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const [tripEvent, setTripEvent] = useState<any>(false)
    const [selectedInfringement, setSelectedInfringement] = useState<any>([])
    const [currentSignature, setCurrentSignature] = useState<any>(false)
    const [currentLocation, setCurrentLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })
    const tripEventModel = new TripEventModel()
    const infringementNoticeModel = new InfringementNoticeModel()
    const infringementNotice_SignatureModel =
        new InfringementNotice_SignatureModel()
    const [closeOnSave, setCloseOnSave] = useState(false)
    const handleTimeChange = (date: any) => {
        setInfringementNotices({
            ...infringementNotices,
            time: dayjs(date).format('HH:mm'),
        })
        setTime(dayjs(date).format('HH:mm'))
    }

    const handleDateChange = (date: any) => {
        setInfringementNotices({
            ...infringementNotices,
            ownerDOB: dayjs(date).format('YYYY-MM-DD'),
        })
        setDate(dayjs(date))
    }

    useEffect(() => {
        setInfringementNotices(false)
        if (selectedEvent) {
            currentEventRef.current = selectedEvent
            setCurrentEvent(selectedEvent)
            getCurrentEvent(selectedEvent?.id)
        }
    }, [selectedEvent])

    useEffect(() => {
        setInfringementNotices(false)
        if (currentEvent) {
            getCurrentEvent(currentEvent?.id)
        }
    }, [currentEvent])

    const getCurrentEvent = async (id: any) => {
        if (offline) {
            const event = await tripEventModel.getById(id)
            if (event) {
                if (!event.infringementNotice) {
                    const infringementNotice =
                        await infringementNoticeModel.getById(
                            event.infringementNoticeID,
                        )
                    event.infringementNotice = infringementNotice
                }
                setTripEvent(event)
                const infringementData = event.infringementNotice
                    .infringementData
                    ? JSON.parse(event.infringementNotice?.infringementData)
                    : []
                setInfringementNotices({
                    ...event.infringementNotice,
                    ...infringementData,
                })

                if (event.infringementNotice?.geoLocationID > 0) {
                    setCurrentLocation(event.infringementNotice?.geoLocation)
                }
                setSelectedInfringement(
                    infringementOptions.filter((option) => {
                        return event.infringementNotice?.infringementUsed?.includes(
                            option.value,
                        )
                    }),
                )
                setContent(event.infringementNotice?.otherDescription)
                event.infringementNotice?.time &&
                    setTime(event.infringementNotice?.time.substring(0, 5))
                event.infringementNotice?.ownerDOB &&
                    setDate(dayjs(event.infringementNotice?.ownerDOB))
            }
        } else {
            getTripEvent({
                variables: {
                    id: id,
                },
            })
        }
    }

    const [getTripEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setTripEvent(event)
                const infringementData = JSON.parse(
                    event.infringementNotice.infringementData,
                )
                setInfringementNotices({
                    ...event.infringementNotice,
                    ...infringementData,
                })

                if (event.infringementNotice?.geoLocationID > 0) {
                    setCurrentLocation(event.infringementNotice?.geoLocation)
                }
                setSelectedInfringement(
                    infringementOptions.filter((option) => {
                        return infringementData?.infringementUsed?.includes(
                            option.value,
                        )
                    }),
                )
                setContent(event.infringementNotice?.otherDescription)
                event.infringementNotice?.time &&
                    setTime(event.infringementNotice?.time.substring(0, 5))
                event.infringementNotice?.ownerDOB &&
                    setDate(dayjs(event.infringementNotice?.ownerDOB))
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const handleEditorChange = (newContent: any) => {
        setInfringementNotices({
            ...infringementNotices,
            otherDescription: newContent,
        })
        setContent(newContent)
    }

    const [updateInfringementNotice_Signature] = useMutation(
        UpdateInfringementNotice_Signature,
        {
            onCompleted: (data) => {},

            onError: (error) => {
                console.error(error)
            },
        },
    )

    const [createInfringementNotice_Signature] = useMutation(
        CreateInfringementNotice_Signature,
        {
            onCompleted: (data) => {},

            onError: (error) => {
                console.error(error)
            },
        },
    )

    useEffect(() => {
        if (infringementNotices) {
            if (infringementNotices.signature)
                delete infringementNotices.signature
            setInfringementNotices(infringementNotices)
        }
    }, [infringementNotices])

    const handleSave = async () => {
        let signatureData = currentSignature
        if (infringementNotices?.signatureID > 0) {
            if (offline) {
                await infringementNotice_SignatureModel.save({
                    id: infringementNotices?.signatureID,
                    signatureData: signatureData.signatureData,
                })
            } else {
                updateInfringementNotice_Signature({
                    variables: {
                        input: {
                            id: infringementNotices?.signatureID,
                            signatureData: signatureData.signatureData,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                const newSignatureID = generateUniqueId()
                signatureData = await infringementNotice_SignatureModel.save({
                    id: newSignatureID,
                    signatureData: signatureData.signatureData,
                })
                setCurrentSignature(signatureData)
            }
        }

        const signatureID =
            signatureData?.id ?? infringementNotices?.signature?.id
        const geoLocationID =
            currentLocation?.value ?? infringementNotices?.geoLocationID

        if (infringementNotices.__typename)
            delete infringementNotices.__typename
        if (infringementNotices.infringementData)
            delete infringementNotices.infringementData
        if (infringementNotices.signature) delete infringementNotices.signature
        if (infringementNotices.geoLocation)
            delete infringementNotices.geoLocation
        if (infringementNotices.waterwaysOfficer)
            delete infringementNotices.waterwaysOfficer
        const variables = {
            input: {
                ...infringementNotices,
                signatureID: signatureID,
                geoLocationID: geoLocationID,
                time: time ?? dayjs().format('HH:mm:ss'),
            },
        }
        if (currentEvent) {
            if (offline) {
                await tripEventModel.save({
                    id: currentEvent.id,
                    eventCategory: 'InfringementNotice',
                    logBookEntrySectionID: currentTrip.id,
                    infringementNoticeID: currentEvent.infringementNoticeID,
                })
                await infringementNoticeModel.save({
                    ...variables.input,
                    id: currentEvent.infringementNoticeID,
                    tripEventID: currentEvent.id,
                })
                if (closeOnSave) {
                    setCloseOnSave(false)
                    closeModal()
                }
            } else {
                updateTripEvent({
                    variables: {
                        input: {
                            id: +currentEvent.id,
                            eventCategory: 'InfringementNotice',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
                updateInfringementNotice({
                    variables: {
                        input: {
                            id: +selectedEvent?.infringementNotice?.id,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                const newID = generateUniqueId()
                await tripEventModel
                    .save({
                        id: newID,
                        eventCategory: 'InfringementNotice',
                        logBookEntrySectionID: currentTrip.id,
                        infringementNoticeID: newID,
                    })
                    .then((response) => {
                        const data = response
                        setTripEvent(data)
                        setInfringementNotices({
                            ...infringementNotices,
                            id: data.id,
                        })
                        infringementNoticeModel
                            .save({
                                ...variables.input,
                                id: data.id,
                                tripEventID: data.id,
                            })
                            .then((response) => {
                                const data = response
                                infringementNotice_SignatureModel
                                    .save({
                                        id: newID,
                                        signatureData:
                                            signatureData.signatureData,
                                        infringementNoticeID: data.id,
                                    })
                                    .then((response) => {
                                        const signature = response
                                        infringementNoticeModel
                                            .save({
                                                id: data.id,
                                                signatureID: signature.id,
                                            })
                                            .then(() => {
                                                if (closeOnSave) {
                                                    setCloseOnSave(false)
                                                    closeModal()
                                                }
                                                updateTripReport({
                                                    id: [
                                                        ...tripReport.map(
                                                            (trip: any) =>
                                                                trip.id,
                                                        ),
                                                        currentTrip.id,
                                                    ],
                                                })
                                            })
                                    })
                            })
                    })
            } else {
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'InfringementNotice',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
        }
    }

    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            const data = response.createTripEvent
            currentEventRef.current = data
            setCurrentEvent(data)
            createInfringementNotice({
                variables: {
                    input: {
                        ...infringementNotices,
                    },
                },
            }).then((response) => {
                const data = response.data.createInfringementNotice
                createInfringementNotice_Signature({
                    variables: {
                        input: {
                            signatureData: currentSignature.signatureData,
                            infringementNoticeID: +data.id,
                        },
                    },
                }).then((response) => {
                    const signature =
                        response.data.createInfringementNotice_Signature
                    updateInfringementNotice({
                        variables: {
                            input: {
                                id: +data.id,
                                signatureID: signature.id,
                            },
                        },
                    })
                })
            })
            updateTripEvent({
                variables: {
                    input: {
                        id: data.id,
                        eventCategory: 'InfringementNotice',
                        infringementNoticeID: currentEventRef.current?.id,
                    },
                },
            })
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
            toast.error('Error', {
                description: 'Failed to create trip event',
            })
        },
    })

    const [createInfringementNotice] = useMutation(CREATE_INFRINGEMENT_NOTICE, {
        onCompleted: (response) => {
            const data = response.createInfringementNotice
            setTimeout(() => {
                setInfringementNotices({
                    ...infringementNotices,
                    id: data.id,
                })
                updateTripEvent({
                    variables: {
                        input: {
                            id: currentEventRef.current?.id,
                            infringementNoticeID: data.id,
                        },
                    },
                })
            }, 200)
            if (closeOnSave) {
                setCloseOnSave(false)
                closeModal()
            }
        },
        onError: (error) => {
            console.error('Error creating refuelling', error)
        },
    })

    const [updateInfringementNotice] = useMutation(UPDATE_INFRINGEMENT_NOTICE, {
        onCompleted: (response) => {
            // const data = response.updateInfringementNotice
            if (closeOnSave) {
                setCloseOnSave(false)
                closeModal()
            }
        },
        onError: (error) => {
            console.error('Error updating refuelling', error)
        },
    })

    const [updateTripEvent] = useMutation(UpdateTripEvent, {
        onCompleted: () => {
            getCurrentEvent(currentEvent?.id)
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
        },
        onError: (error) => {
            console.error('Error updating trip event', error)
            toast.error('Error', {
                description: 'Failed to update trip event',
            })
        },
    })

    const handleLocationChange = (value: any) => {
        // If value is null or undefined, clear the location
        if (!value) {
            setInfringementNotices({
                ...infringementNotices,
                geoLocationID: 0,
                lat: null,
                long: null,
            })
            return
        }

        // Check if the value is from dropdown selection (has 'value' property)
        if (value.value) {
            // Handle location selected from dropdown
            setInfringementNotices({
                ...infringementNotices,
                geoLocationID: +value.value,
                lat: null,
                long: null,
            })

            // If the value object has latitude and longitude, update currentLocation
            if (value.latitude !== undefined && value.longitude !== undefined) {
                setCurrentLocation({
                    latitude: value.latitude,
                    longitude: value.longitude,
                })
            }
        } else if (
            value.latitude !== undefined &&
            value.longitude !== undefined
        ) {
            // Handle direct coordinates input
            setInfringementNotices({
                ...infringementNotices,
                geoLocationID: 0, // Reset geoLocationID when using direct coordinates
                lat: value.latitude,
                long: value.longitude,
            })

            // Update currentLocation
            setCurrentLocation({
                latitude: value.latitude,
                longitude: value.longitude,
            })
        }
    }

    const infringementOptions = [
        { label: 'Life Jacket infringement issued', value: 'lifeJacket' },
        {
            label: 'Speed / navigation infringement issued',
            value: 'speedOrNavigation',
        },
        { label: 'Towing infringement issued', value: 'towing' },
        {
            label: 'Swimming / diving infringement issued',
            value: 'swimmingOrDiving',
        },
        {
            label: 'Mooring / embarkation / ramps or jetty infringements',
            value: 'mooring',
        },
        { label: 'Other', value: 'other' },
    ]

    const getInfringementFields = (infringement: string) => {
        switch (infringement) {
            case 'lifeJacket':
                return [
                    {
                        value: 'FailingToCarryLifejackets',
                        label: 'Failing to carry accessible and sufficient lifejackets of appropriate size for each person on board the vessel',
                    },
                    {
                        value: 'FailingToWearLifejacketWhenInstructed',
                        label: 'Failing to wear properly secured lifejacket of appropriate size when instructed by person in charge of recreational vessel',
                    },
                    {
                        value: 'FailingToEnsureLifejacketOnVessel6mOrLess',
                        label: 'Failing to ensure persons on recreational vessel 6 metres or less wear a proper lifejacket when the vessel is making way',
                    },
                    {
                        value: 'FailingToEnsureLifejacketForChildrenOnVesselGreaterThan6m',
                        label: 'Failing to ensure persons 10 years or younger on recreational vessel greater than 6 metres wear a proper lifejacket at all times',
                    },
                    {
                        value: 'FailingToEnsureLifejacketOnJetBoat',
                        label: 'Failing to ensure persons on a recreational jet boat wear proper lifejacket when making way',
                    },
                    {
                        value: 'FailingToEnsureLifejacketInDanger',
                        label: 'Failing to ensure persons on a recreational vessel wear a proper lifejacket in a situation of danger or risk',
                    },
                    {
                        value: 'TowingWithoutLifejacketOver5Knots',
                        label: 'Towing a person who is not wearing a properly secured lifejacket of appropriate size while the vessel is doing a speed exceeding 5 knots, or being towed by a vessel at a speed exceeding 5 knots while not wearing a properly secured lifejacket of appropriate size',
                    },
                ]
            case 'speedOrNavigation':
                return [
                    {
                        value: 'UnsupervisedUnderagePersonOperatingVessel',
                        label: 'Being an unsupervised underage person operating a powered vessel capable of exceeding 10 knots',
                    },
                    {
                        value: 'AllowingUnsupervisedUnderagePerson',
                        label: 'Allowing an unsupervised underage person to operate powered vessel capable of exceeding 10 knots',
                    },
                    {
                        value: 'Exceeding5KnotRestriction50Metres',
                        label: 'Exceeding the 5-knot speed restriction within 50 metres of vessel, floating structure, or person',
                    },
                    {
                        value: 'Exceeding5KnotRestriction200MetresShore',
                        label: 'Exceeding the 5-knot speed restriction within 200 metres of shore or structure',
                    },
                    {
                        value: 'Exceeding5KnotRestriction200MetresFlagA',
                        label: 'Exceeding the 5-knot speed restriction within 200 metres of vessel flying Flag A of International Code of Signals',
                    },
                    {
                        value: 'Exceeding5KnotRestrictionBodyExtending',
                        label: 'Exceeding 5-knot speed restriction while person has part of body extending from powered vessel',
                    },
                    {
                        value: 'BeingTowedExceeding5Knots',
                        label: 'Being towed at speed of more than 5 knots in restricted-speed locations',
                    },
                    {
                        value: 'NavigatingWithoutDueCare',
                        label: 'Navigating vessel without due care and caution or at speed or in manner so as to endanger any person',
                    },
                    {
                        value: 'FailingToKeepStarboard',
                        label: 'Failing to ensure vessel keeps to starboard side of river channel',
                    },
                    {
                        value: 'FailingToGiveWayDownstream',
                        label: 'Failing to give way to vessel coming downstream when going upstream',
                    },
                    {
                        value: 'OperatingInUnsafeConditions',
                        label: 'Operating vessel in conditions that do not permit safe operation',
                    },
                    {
                        value: 'Exceeding5KnotLimitLakes',
                        label: 'Exceeding 5-knot speed limit on specified lakes in powered vessel',
                    },
                ]
            case 'towing':
                return [
                    {
                        value: 'TowingExceeding5KnotsNoLookout',
                        label: 'Towing person from vessel at speed exceeding 5 knots without lookout of appropriate age',
                    },
                    {
                        value: 'BeingTowedExceeding5KnotsNoLookout',
                        label: 'Being towed from vessel at speed exceeding 5 knots without lookout of appropriate age',
                    },
                    {
                        value: 'TowingExceeding5KnotsNight',
                        label: 'Towing person from vessel between sunset and sunrise or in restricted visibility',
                    },
                    {
                        value: 'BeingTowedExceeding5KnotsNight',
                        label: 'Being towed from vessel between sunset and sunrise',
                    },
                    {
                        value: 'ParasailingFranktonArm',
                        label: 'Lake Wakatipu: operating vessel involved in parasailing in Frankton arm of lake',
                    },
                ]
            case 'swimmingOrDiving':
                return [
                    {
                        value: 'CreatingNuisance',
                        label: 'Creating nuisance through use or control of vessel, speed of vessel, or speed of item or object towed behind or used with vessel',
                    },
                    {
                        value: 'SwimmingNearJettyWithNoSwimmingSign',
                        label: 'Swimming, diving, jumping, or related activities from or within 50 metres of jetty or wharf with “no swimming” signage',
                    },
                    {
                        value: 'SwimmingInHarbourmasterArea',
                        label: 'Swimming or diving in area identified by harbourmaster',
                    },
                ]
            case 'mooring':
                return [
                    {
                        value: 'EmbarkingOrDisembarkingWhileVesselIsMakingWay',
                        label: 'Embarking or disembarking while vessel is making way',
                    },
                    {
                        value: 'AnchoringVesselInMannerThatObstructsPassageOfVessels',
                        label: 'Anchoring vessel in manner that obstructs passage of vessels or obstructs approach to wharf, pier, or jetty, or creates hazard to vessels at anchor',
                    },
                    {
                        value: 'FailingToMoorVesselInSecureManner',
                        label: 'Failing to moor vessel in secure manner or without adequate or safe means of accessing vessel',
                    },
                    {
                        value: 'PlacingObstructionInWatersLikelyToRestrictNavigation',
                        label: 'Placing obstruction in waters likely to restrict navigation, or cause injury, death, or damage',
                    },
                    {
                        value: 'TyingVesselToNavigationAidWithoutPermission',
                        label: 'Tying vessel to navigation aid without permission',
                    },
                    {
                        value: 'DamagingRemovingDefacingOrInterferingWithNavigationAid',
                        label: 'Damaging, removing, defacing, or otherwise interfering with navigation aid',
                    },
                    {
                        value: 'ObstructingUseOfJettyWharfRampOrLaunchFacility',
                        label: 'Obstructing use of jetty, wharf, ramp, or launch facility owned or operated by Queenstown Lakes District Council',
                    },
                    {
                        value: 'RefuellingVesselWithPassengersOnBoard',
                        label: 'Refuelling vessel with passengers on board',
                    },
                ]
            case 'other':
                return [
                    {
                        value: 'PermittingVesselToContinueAfterWaterSkiDropped',
                        label: 'Permitting vessel to continue onwards after water ski or similar object dropped by person being towed',
                    },
                    {
                        value: 'FailingToEnsureWakeSafety',
                        label: 'Failing to ensure wake does not prevent people from safely using waterway, or does not cause danger or risk of danger, or does not cause risk of harm',
                    },
                    {
                        value: 'CreatingNuisanceThroughVesselUse',
                        label: 'Creating nuisance through use or control of vessel, speed of vessel, or speed of item or object towed behind or used with vessel',
                    },
                    {
                        value: 'FailingToKeepVesselSeaworthy',
                        label: 'Failing to keep vessel in seaworthy condition or leaving vessel sunk, stranded, or abandoned',
                    },
                    {
                        value: 'FailingToConductHotWorkSafely',
                        label: 'Failing to conduct hot work operations in accordance with Code of Safe Working Practices for Merchant Seafarers',
                    },
                    {
                        value: 'FailingToTakeFirePrecautions',
                        label: 'Failing to take fire precautions before or during hot work operations',
                    },
                    {
                        value: 'FailingToMaintainDangerousGoodsRecord',
                        label: 'Failing to maintain or make available written record of dangerous goods loaded or unloaded onto vessel',
                    },
                    {
                        value: 'FailingToApplyForOrganisedWaterActivity',
                        label: 'Failing to apply to the harbourmaster when intending to conduct organised water activity',
                    },
                ]
            default:
                return []
        }
    }

    const onSignatureChanged = (sign: any) => {
        currentSignature
            ? setCurrentSignature({ ...currentSignature, signatureData: sign })
            : setCurrentSignature({ signatureData: sign })
    }

    return (
        <div className="space-y-[96px]">
            <div className="space-y-6">
                {crewMembers && (
                    <Label
                        label="Waterways officer"
                        htmlFor="waterways-officer"
                        className="w-full">
                        <Combobox
                            id="waterways-officer"
                            options={crewMembers?.map((member: any) => ({
                                label: `${member.crewMember.firstName} ${member.crewMember.surname}`,
                                value: member.crewMember.id,
                            }))}
                            placeholder="Select Waterways Officer"
                            onChange={(value: any) => {
                                setInfringementNotices({
                                    ...infringementNotices,
                                    waterwaysOfficerID: value?.value,
                                })
                            }}
                            value={
                                crewMembers?.find(
                                    (member: any) =>
                                        member.crewMember.id ==
                                        infringementNotices?.waterwaysOfficerID,
                                ) && {
                                    label: `${
                                        crewMembers?.find(
                                            (member: any) =>
                                                member.crewMember.id ==
                                                infringementNotices?.waterwaysOfficerID,
                                        )?.crewMember.firstName
                                    } ${
                                        crewMembers?.find(
                                            (member: any) =>
                                                member.crewMember.id ==
                                                infringementNotices?.waterwaysOfficerID,
                                        )?.crewMember.surname
                                    }`,
                                    value: crewMembers?.find(
                                        (member: any) =>
                                            member.crewMember.id ==
                                            infringementNotices?.waterwaysOfficerID,
                                    )?.crewMember.id,
                                }
                            }
                            disabled={locked}
                        />
                    </Label>
                )}
                <Label
                    label="Time of infringement"
                    htmlFor="time-of-infringement"
                    disabled={locked}
                    className="w-full">
                    <TimeField
                        time={time}
                        handleTimeChange={handleTimeChange}
                        timeID="fuel-added-time"
                        fieldName="Time"
                    />
                </Label>
                <Label
                    label="Location of infringement"
                    htmlFor="location"
                    disabled={locked}
                    className="w-full">
                    <LocationField
                        offline={offline}
                        setCurrentLocation={setCurrentLocation}
                        handleLocationChange={handleLocationChange}
                        currentEvent={tripEvent.infringementNotice}
                    />
                </Label>
                <Separator className="my-4" />
                <H5>INFRINGING VESSEL DETAILS</H5>
                <Label
                    label="Vessel type / description"
                    htmlFor="vessel-type"
                    disabled={locked}
                    className="w-full">
                    <Input
                        id="vessel-type"
                        type="text"
                        placeholder="Vessel type / description"
                        value={infringementNotices?.vesselType ?? ''}
                        onChange={(e) => {
                            setInfringementNotices({
                                ...infringementNotices,
                                vesselType: e.target.value,
                            })
                        }}
                        disabled={locked}
                    />
                </Label>
                <Label
                    label="Vessel name"
                    htmlFor="vessel-name"
                    disabled={locked}
                    className="w-full">
                    <Input
                        id="vessel-name"
                        type="text"
                        placeholder="Vessel name"
                        value={infringementNotices?.vesselName ?? ''}
                        onChange={(e) => {
                            setInfringementNotices({
                                ...infringementNotices,
                                vesselName: e.target.value,
                            })
                        }}
                        disabled={locked}
                    />
                </Label>
                <Label
                    label="MNZ# / registration"
                    htmlFor="vessel-reg"
                    disabled={locked}
                    className="w-full">
                    <Input
                        id="vessel-reg"
                        type="text"
                        placeholder="MNZ# / registration"
                        value={infringementNotices?.vesselReg ?? ''}
                        onChange={(e) => {
                            setInfringementNotices({
                                ...infringementNotices,
                                vesselReg: e.target.value,
                            })
                        }}
                        disabled={locked}
                    />
                </Label>
                <Separator className="my-4" />
                <H5>OWNER / USER DETAILS</H5>
                <Label
                    label="Full name"
                    htmlFor="owner-name"
                    disabled={locked}
                    className="w-full">
                    <Input
                        id="owner-name"
                        type="text"
                        placeholder="Full name"
                        value={infringementNotices?.ownerFullName ?? ''}
                        onChange={(e) => {
                            setInfringementNotices({
                                ...infringementNotices,
                                ownerFullName: e.target.value,
                            })
                        }}
                        disabled={locked}
                    />
                </Label>
                <Label
                    label="Date of birth"
                    htmlFor="dob"
                    disabled={locked}
                    className="w-full">
                    <DatePicker
                        id="dob"
                        mode="single"
                        type="date"
                        value={date ? date.toDate() : undefined}
                        onChange={(newDate) => handleDateChange(dayjs(newDate))}
                        placeholder="Select date of birth"
                        disabled={locked}
                    />
                </Label>
                <Label
                    label="Occupation"
                    htmlFor="owner-occupation"
                    disabled={locked}
                    className="w-full">
                    <Input
                        id="owner-occupation"
                        type="text"
                        placeholder="Occupation"
                        value={infringementNotices?.ownerOccupation ?? ''}
                        onChange={(e) => {
                            setInfringementNotices({
                                ...infringementNotices,
                                ownerOccupation: e.target.value,
                            })
                        }}
                        disabled={locked}
                    />
                </Label>
                <Label
                    label="Address"
                    htmlFor="owner-address"
                    disabled={locked}
                    className="w-full">
                    <Input
                        id="owner-address"
                        type="text"
                        placeholder="Address"
                        value={infringementNotices?.ownerAddress ?? ''}
                        onChange={(e) => {
                            setInfringementNotices({
                                ...infringementNotices,
                                ownerAddress: e.target.value,
                            })
                        }}
                        disabled={locked}
                    />
                </Label>
                <Label
                    label="Phone number"
                    htmlFor="owner-phone"
                    disabled={locked}
                    className="w-full">
                    <Input
                        id="owner-phone"
                        type="text"
                        placeholder="Phone"
                        value={infringementNotices?.ownerPhone ?? ''}
                        onChange={(e) => {
                            setInfringementNotices({
                                ...infringementNotices,
                                ownerPhone: e.target.value,
                            })
                        }}
                        disabled={locked}
                    />
                </Label>
                <Label
                    label="Email address"
                    htmlFor="owner-email"
                    disabled={locked}
                    className="w-full">
                    <Input
                        id="owner-email"
                        type="email"
                        placeholder="Email"
                        value={infringementNotices?.ownerEmail ?? ''}
                        onChange={(e) => {
                            setInfringementNotices({
                                ...infringementNotices,
                                ownerEmail: e.target.value,
                            })
                        }}
                        disabled={locked}
                    />
                </Label>
            </div>
            <div className="space-y-6">
                <div>
                    <H5>INFRINGEMENT DETAILS</H5>
                    <P>Choose the type of infringement</P>
                </div>

                <Label
                    label="Infringement incurred"
                    htmlFor="infringement-type"
                    disabled={locked}
                    className="w-full">
                    <Combobox
                        id="infringement-type"
                        options={infringementOptions}
                        placeholder="Select Infringement"
                        onChange={(value: any) => {
                            setSelectedInfringement(value)
                            const infringementNotice = {
                                ...infringementNotices,
                                infringementUsed: Array.isArray(value)
                                    ? value.map((item: any) => item.value)
                                    : [value.value],
                            }
                            setInfringementNotices(infringementNotice)
                        }}
                        value={selectedInfringement}
                        multi={true}
                        disabled={locked}
                    />
                </Label>
                {infringementNotices?.infringementUsed &&
                    visibility &&
                    infringementOptions.map(
                        (option: any, iindex: number) =>
                            infringementNotices.infringementUsed.includes(
                                option.value,
                            ) && (
                                <div
                                    key={iindex}
                                    className={`${infringementNotices.infringementUsed.includes(option.value) ? '' : 'hidden'}`}>
                                    <Label
                                        label={
                                            infringementOptions.find(
                                                (item) =>
                                                    item.value === option.value,
                                            )?.label
                                        }
                                        htmlFor={`infringement-${option.value}`}
                                        disabled={locked}
                                        className="w-full"></Label>
                                    {getInfringementFields(option.value).map(
                                        (field: any, index: number) => (
                                            <div key={index} className="my-4">
                                                <CheckFieldLabel
                                                    type="checkbox"
                                                    variant="warning"
                                                    id={`${currentEvent?.id}${iindex}-${field.value}-onChangeComplete`}
                                                    checked={
                                                        !!infringementNotices[
                                                            option.value
                                                        ]?.find(
                                                            (item: any) =>
                                                                item ==
                                                                field.value,
                                                        )
                                                    }
                                                    onCheckedChange={(
                                                        checked,
                                                    ) => {
                                                        setInfringementNotices({
                                                            ...infringementNotices,
                                                            [option.value]:
                                                                checked
                                                                    ? infringementNotices[
                                                                          option
                                                                              .value
                                                                      ]
                                                                          ?.length >
                                                                      0
                                                                        ? [
                                                                              ...infringementNotices[
                                                                                  option
                                                                                      .value
                                                                              ],
                                                                              field.value,
                                                                          ]
                                                                        : [
                                                                              field.value,
                                                                          ]
                                                                    : infringementNotices[
                                                                          option
                                                                              .value
                                                                      ]?.filter(
                                                                          (
                                                                              item: any,
                                                                          ) =>
                                                                              item !==
                                                                              field.value,
                                                                      ),
                                                        })
                                                    }}
                                                    disabled={locked}
                                                    label={field.label}
                                                />
                                            </div>
                                        ),
                                    )}
                                </div>
                            ),
                    )}
                {infringementNotices?.infringementUsed?.includes('other') && (
                    <Label
                        label="Other description"
                        htmlFor="other-description"
                        disabled={locked}
                        className="w-full">
                        <div className="mt-2">
                            {(!currentEvent || infringementNotices) && (
                                <Editor
                                    id="other-description"
                                    placeholder="Other description"
                                    className="!w-full ring-1 ring-inset"
                                    content={content}
                                    handleEditorChange={handleEditorChange}
                                    disabled={locked}
                                />
                            )}
                        </div>
                    </Label>
                )}
                <div className="w-full flex flex-col space-y-2">
                    <CloudFlareCaptures
                        inputId={selectedEvent?.id || 0}
                        sectionId={currentTrip.id}
                        buttonType={'button'}
                        sectionName={'tripEventID'}
                    />
                </div>
                <SignaturePad
                    title="Signature"
                    signature={infringementNotices?.signature}
                    onSignatureChanged={onSignatureChanged}
                    locked={locked}
                    description="Sign here to confirm the infringement notice"
                />
            </div>

            <div className="flex justify-end gap-2">
                <Button
                    variant="back"
                    iconLeft={ArrowLeft}
                    onClick={closeModal}>
                    Cancel
                </Button>

                <Button
                    variant="primary"
                    iconLeft={Check}
                    onClick={
                        locked
                            ? () => {}
                            : () => {
                                  setCloseOnSave(true)
                                  handleSave()
                              }
                    }
                    disabled={locked}>
                    {selectedEvent ? 'Update' : 'Save'}
                </Button>
            </div>
        </div>
    )
}
