'use client'
import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import AWS from 'aws-sdk'

import { AlertDialogNew } from '@/components/ui'
import FileUploadUI, { BaseFile } from '@/components/ui/file-upload-ui'
import { toast } from 'sonner'

const ACCOUNT_ID = 'ddde1c1cd1aa25641691808dcbafdeb7'
const ACCESS_KEY_ID = '06c3e13a539f24e6fdf7075bf381bf5e'
const SECRET_ACCESS_KEY =
    '0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8'

const s3Client = new AWS.S3({
    endpoint: `https://${ACCOUNT_ID}.r2.cloudflarestorage.com`,
    accessKeyId: ACCESS_KEY_ID,
    secretAccessKey: SECRET_ACCESS_KEY,
    signatureVersion: 'v4',
    region: 'auto',
})

// Type definitions
interface CloudFlareFile extends BaseFile {
    title: string
}

interface CloudFlareImages {
    fieldName?: string
    id?: number
    imageType?: string
    logBookEntryID?: number
    logBookEntrySectionID?: number
    name?: string
    imageData?: string
}

interface UploadCloudFlareProps {
    files?: CloudFlareFile[]
    setFiles: (
        files:
            | CloudFlareFile[]
            | ((prev: CloudFlareFile[]) => CloudFlareFile[]),
    ) => void
    multipleUpload?: boolean
    text?: string
    subText?: string
    bgClass?: string
    accept?: string
    bucketName?: string
    prefix?: string
    displayFiles?: boolean
    displayImageFile?: boolean
    displayDropZone?: boolean
    // handleSetFile?: (file: CloudFlareFile[]) => void
}

export default function UploadCloudFlare({
    files = [],
    setFiles,
    multipleUpload = true,
    text = 'Documents and Images',
    subText,
    bgClass = '',
    accept = '.xlsx,.xls,image/*,.doc, .docx,.ppt, .pptx,.txt,.pdf,.csv',
    bucketName = 'sealogs',
    prefix = '',
    displayFiles = true,
    displayImageFile = false,
    displayDropZone = true,
}: UploadCloudFlareProps) {
    /* ------------------------------------------------------- */
    /* state                                                   */
    /* ------------------------------------------------------- */
    const [imageLoader, setImageLoader] = useState<boolean>(false)
    const [image, setImage] = useState<string>('')
    const [renderImage, setRenderImage] = useState<string>(
        '/sealogs-document_upload.svg',
    )
    const [displayImage, setDisplayImage] = useState<boolean>(false)
    const [clientID, setClientID] = useState<number>(0)

    useEffect(() => {
        setClientID(+(localStorage.getItem('clientId') ?? 0))
        // if (displayImageFile) {
        //     if (files.length > 0 && !files[0].imageData) {
        //         getFile({ name: files[0].title } as CloudFlareImages)
        //     }
        // }
    }, [])

    const uploadFile = async (file: File) => {
        const fileName = clientID + '-' + prefix + file.name
        const isFileExists = files?.some(
            (existingFile: CloudFlareFile) => existingFile.title === fileName,
        )

        if (isFileExists) {
            toast.error('File with same name already exists!')
            return
        }

        setImageLoader(true)

        // Upload file to Cloudflare
        s3Client.putObject(
            {
                Bucket: bucketName,
                Key: fileName,
                Body: file,
            },
            (err, _data) => {
                setImageLoader(false)
                if (err) {
                    console.error(err)
                    toast.error('Failed to upload file')
                } else {
                    const newFile: CloudFlareFile = { title: fileName }
                    if (multipleUpload) {
                        const existingFiles = [...files, newFile]
                        setFiles(existingFiles)
                    } else {
                        setFiles([newFile])
                    }
                }
            },
        )
    }

    /* ------------------------------------------------------- */
    /* event handlers                                          */
    /* ------------------------------------------------------- */
    const handleFilesSelected = (fileList: FileList) => {
        const arr = Array.from(fileList)
        arr.forEach(uploadFile)
    }

    const handleFileClick = (file: CloudFlareFile) => {
        // Download file from Cloudflare
        s3Client.getObject(
            {
                Bucket: bucketName,
                Key: file.title,
            },
            async (err, data) => {
                if (err) {
                    console.error(err)
                    toast.error('Failed to download file')
                } else {
                    const fileType = file.title.split('.').pop() || ''
                    const blob = new Blob([data?.Body as Uint8Array])
                    const url = URL.createObjectURL(blob)

                    if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {
                        setImage(url)
                        setDisplayImage(true)
                    } else if (fileType.match(/^(pdf)$/i)) {
                        const pdfBlob = new Blob([data?.Body as Uint8Array], {
                            type: 'application/pdf',
                        })
                        const pdfUrl = URL.createObjectURL(pdfBlob)
                        window.open(pdfUrl, '_blank')
                        URL.revokeObjectURL(pdfUrl)
                    } else {
                        toast.error(
                            'File type not supported to view. Please save the file to view.',
                        )
                        const link = document.createElement('a')
                        link.target = '_blank'
                        link.href = url
                        link.download = file.title
                        link.click()
                        URL.revokeObjectURL(url)
                    }
                }
            },
        )
    }

    // const getFile = (file: CloudFlareImages) => {
    //     if (!file || !file.name) {
    //         console.error('No file name provided')
    //         return
    //     }
    //     s3Client.getObject(
    //         {
    //             Bucket: bucketName,
    //             Key: file.name,
    //         },
    //         async (err, data) => {
    //             if (err) {
    //                 console.error(err)
    //             } else {
    //                 if (!file || !file.name) {
    //                     console.error('No file name provided')
    //                     return
    //                 }
    //                 const fileType = file.name.split('.').pop() || ''
    //                 const blob = new Blob([
    //                     new Uint8Array(data?.Body as ArrayBuffer),
    //                 ])
    //                 const url = URL.createObjectURL(blob)
    //                 console.log('File URL:', url)
    //                 setFiles([
    //                     { title: file.name, imageData: url } as CloudFlareFile,
    //                 ])
    //             }
    //         },
    //     )
    // }

    /* ------------------------------------------------------- */
    /* custom file item renderer                               */
    /* ------------------------------------------------------- */
    const renderFileItem = (file: CloudFlareFile, index: number) => (
        <div key={index}>
            {displayImageFile ? (
                <Image
                    src={
                        files[0].title
                            ? 'https://assets.sealogs.com/' + files[0].title
                            : renderImage
                    }
                    alt="Document"
                    width={32}
                    height={16}
                    className="object-cover border rounded w-32 h-16"
                />
            ) : (
                <div
                    key={index}
                    onClick={() => handleFileClick(file)}
                    className="flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden">
                    <Image
                        src="/sealogs-document_upload.svg"
                        alt="Document"
                        width={48}
                        height={48}
                        className="mb-2"
                    />
                    <div className="text-xs text-center break-all text-muted-foreground">
                        {file.title.replace(clientID + '-', '')}
                    </div>
                </div>
            )}
        </div>
    )

    /* ------------------------------------------------------- */
    /* render                                                  */
    /* ------------------------------------------------------- */
    return (
        <FileUploadUI
            files={files}
            onFilesSelected={handleFilesSelected}
            text={text}
            subText={subText}
            bgClass={bgClass}
            multipleUpload={multipleUpload}
            acceptedFileTypes={accept}
            isLoading={imageLoader}
            renderFileItem={renderFileItem}
            displayFiles={displayFiles}
            onFileClick={handleFileClick}
            displayDropZone={displayDropZone}>
            {/* Image preview dialog */}
            <AlertDialogNew
                openDialog={displayImage}
                setOpenDialog={setDisplayImage}
                noButton
                actionText="Close"
                title="Image Preview">
                <div className="flex items-center justify-center">
                    <img
                        src={image}
                        alt="Preview"
                        className="max-w-full max-h-96 object-contain"
                    />
                </div>
            </AlertDialogNew>
        </FileUploadUI>
    )
}
