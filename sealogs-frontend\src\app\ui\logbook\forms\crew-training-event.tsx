'use client'
import { Check, Calendar, ArrowLeft } from 'lucide-react'
import dayjs from 'dayjs'
import { debounce, isEmpty } from 'lodash'
import { useEffect, useState } from 'react'
import CrewDropdown from '../../../../components/filter/components/crew-dropdown/crew-dropdown'
import { TrainingSessionFormSkeleton } from '../../../../components/skeletons'
import CrewMultiSelectDropdown from '../../crew/multiselect-dropdown/multiselect-dropdown'
import SignatureCanvas from 'react-signature-canvas'
import { getSignatureUrl } from '@/app/lib/actions'
import SignaturePad from '@/components/signature-pad'
import {
    CREATE_TRAINING_SESSION,
    UPDATE_TRAINING_SESSION,
    CREATE_MEMBER_TRAINING_SIGNATURE,
    UPDATE_MEMBER_TRAINING_SIGNATURE,
    CREATE_TRAINING_SESSION_DUE,
    UPDATE_TRAINING_SESSION_DUE,
    CreateTripEvent,
    UpdateTripEvent,
    CREATE_CUSTOMISED_COMPONENT_FIELD_DATA,
    UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA,
} from '@/app/lib/graphQL/mutation'
import { useMediaQuery } from '@reactuses/core'
import {
    GET_MEMBER_TRAINING_SIGNATURES,
    GetTripEvent,
    READ_ONE_TRAINING_SESSION_DUE,
    TRAINING_SESSION_BY_ID,
} from '@/app/lib/graphQL/query'
import { useMutation, useLazyQuery } from '@apollo/client'
import { getTrainingTypeByID, getTrainingTypes } from '@/app/lib/actions'
import { Combobox } from '@/components/ui/comboBox'

import TrainingTypeMultiSelectDropdown from '../../crew-training/type-multiselect-dropdown'
import LocationField from '../components/location/location'
import TimeField from '../components/time'
import Editor from '../../editor'
import TrainingTypeModel from '@/app/offline/models/trainingType'
import MemberTraining_SignatureModel from '@/app/offline/models/memberTraining_Signature'
import TripEventModel from '@/app/offline/models/tripEvent'
import TrainingSessionDueModel from '@/app/offline/models/trainingSessionDue'
import TrainingSessionModel from '@/app/offline/models/trainingSession'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
    SheetBody,
} from '@/components/ui/sheet'
import { H4 } from '@/components/ui'

const CrewTrainingEvent = ({
    trainingTypeId = 0,
    vesselId = 0,
    selectedEvent = false,
    currentTrip = false,
    closeModal,
    updateTripReport,
    tripReport,
    crewMembers,
    masterID,
    logBookConfig,
    vessels,
    locked,
    offline = false,
    logBookStartDate,
}: {
    trainingTypeId: number
    vesselId: number
    selectedEvent: any
    currentTrip: any
    closeModal: any
    updateTripReport: any
    tripReport: any
    crewMembers: any
    masterID: any
    logBookConfig: any
    vessels: any
    locked: any
    offline?: boolean
    logBookStartDate: any
}) => {
    const [trainingID, setTrainingID] = useState<any>(0)
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const [training, setTraining] = useState<any>({})
    const [content, setContent] = useState<any>('')
    const [rawTraining, setRawTraining] = useState<any>()
    const [trainingDate, setTrainingDate] = useState(
        new Date().toLocaleDateString(),
    )
    const isWide = useMediaQuery('(min-width: 640px)')
    const [hasFormErrors, setHasFormErrors] = useState(false)
    const [selectedMemberList, setSelectedMemberList] = useState([] as any[])
    const [signatureMembers, setSignatureMembers] = useState([] as any[])
    const [vesselList, setVesselList] = useState<any>()
    const [trainingTypes, setTrainingTypes] = useState<any>([])
    const [openViewProcedure, setOpenViewProcedure] = useState(false)
    const [openDescriptionPanel, setOpenDescriptionPanel] = useState(false)
    const [descriptionPanelContent, setDescriptionPanelContent] = useState('')
    const [descriptionPanelHeading, setDescriptionPanelHeading] = useState('')
    const [bufferProcedureCheck, setBufferProcedureCheck] = useState<any>([])
    const [bufferFieldComment, setBufferFieldComment] = useState<any>([])
    const [currentComment, setCurrentComment] = useState<any>('')
    const [currentField, setCurrentField] = useState<any>('')
    const [currentFieldComment, setCurrentFieldComment] = useState<any>('')
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [formErrors, setFormErrors] = useState({
        TrainingTypes: '',
        TrainerID: '',
        VesselID: '',
        Date: '',
    })
    const [startTime, setStartTime] = useState<any>(dayjs().format('HH:mm'))
    const [finishTime, setFinishTime] = useState<any>(dayjs().format('HH:mm'))
    const memberIdOptions = [
        masterID,
        ...(Array.isArray(crewMembers)
            ? crewMembers.map((m: any) => m.crewMemberID)
            : []),
    ]
    const [currentLocation, setCurrentLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })

    const trainingTypeModel = new TrainingTypeModel()
    const memberTraining_SignatureModel = new MemberTraining_SignatureModel()
    const tripEventModel = new TripEventModel()
    const trainingSessionDueModel = new TrainingSessionDueModel()
    const trainingSessionModel = new TrainingSessionModel()

    if (!offline) {
        getTrainingTypes(setTrainingTypes)
    }

    const handleSetTraining = (t: any) => {
        const tDate = new Date(t.date).toLocaleDateString()
        setTrainingDate(tDate)
        const trainingData = {
            ID: trainingID,
            Date: dayjs(t.date).format('YYYY-MM-DD'),
            Members: t.members.nodes.map((m: any) => m.id),
            TrainerID: t.trainer.id,
            trainingSummary: t.trainingSummary,
            TrainingTypes: t.trainingTypes.nodes.map((t: any) => t.id),
            VesselID: vesselId,
            FuelLevel: t.fuelLevel || 0,
            GeoLocationID: t.geoLocationID,
            StartTime: t.startTime,
            FinishTime: t.finishTime,
            Lat: t.lat,
            Long: t.long,
        }
        setContent(t.trainingSummary)
        setStartTime(t.startTime)
        setFinishTime(t.finishTime)
        setRawTraining(t)
        setTraining(trainingData)
        if (+t.geoLocationID > 0) {
            setCurrentLocation({
                latitude: t.geoLocation.lat,
                longitude: t.geoLocation.long,
            })
        } else {
            setCurrentLocation({
                latitude: t.lat,
                longitude: t.long,
            })
        }

        const members =
            t.members.nodes.map((m: any) => ({
                label: `${m.firstName ?? ''} ${m.surname ?? ''}`,
                value: m.id,
            })) || []
        setSelectedMemberList(members)
        const signatures = t.signatures.nodes.map((s: any) => ({
            MemberID: s.member.id,
            SignatureData: s.signatureData,
            ID: s.id,
        }))
        setSignatureMembers(signatures)
    }

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    // const handleSetVessels = (data: any) => {
    //     const activeVessels = data?.filter((vessel: any) => !vessel.archived)
    //     const formattedData = [
    //         {
    //             label: 'Other',
    //             value: 'Other',
    //         },
    //         {
    //             label: 'Desktop/shore',
    //             value: 'Onshore',
    //         },
    //         ...activeVessels.map((vessel: any) => ({
    //             value: vessel.id,
    //             label: vessel.title,
    //         })),
    //     ]
    //     setVessels(formattedData)
    // }

    useEffect(() => {
        if (vessels) {
            const activeVessels = vessels?.filter(
                (vessel: any) => !vessel.archived,
            )
            const formattedData = [
                {
                    label: 'Other',
                    value: 'Other',
                },
                {
                    label: 'Desktop/shore',
                    value: 'Onshore',
                },
                ...activeVessels.map((vessel: any) => ({
                    value: vessel.id,
                    label: vessel.title,
                })),
            ]
            setVesselList(formattedData)
        }
    }, [vessels])

    // const [queryVessels] = useLazyQuery(VESSEL_LIST, {
    //     fetchPolicy: 'cache-and-network',
    //     onCompleted: (queryVesselResponse: any) => {
    //         if (queryVesselResponse.readVessels.nodes) {
    //             handleSetVessels(queryVesselResponse.readVessels.nodes)
    //         }
    //     },
    //     onError: (error: any) => {
    //         console.error('queryVessels error', error)
    //     },
    // })

    const [
        mutationCreateTrainingSession,
        { loading: mutationCreateTrainingSessionLoading },
    ] = useMutation(CREATE_TRAINING_SESSION, {
        onCompleted: (response: any) => {
            const data = response.createTrainingSession
            if (data.id > 0) {
                if (bufferProcedureCheck.length > 0) {
                    const procedureFields = bufferProcedureCheck.map(
                        (procedureField: any) => {
                            return {
                                status: procedureField.status ? 'Ok' : 'Not_Ok',
                                trainingSessionID: data.id,
                                customisedComponentFieldID:
                                    procedureField.fieldId,
                                comment: bufferFieldComment.find(
                                    (comment: any) =>
                                        comment.fieldId ==
                                        procedureField.fieldId,
                                )?.comment,
                            }
                        },
                    )
                    procedureFields.forEach((procedureField: any) => {
                        createCustomisedComponentFieldData({
                            variables: {
                                input: procedureField,
                            },
                        })
                    })
                }
                setTrainingID(data.id)
                updateTrainingSessionDues()
                updateSignatures(data.id)
                handleEditorChange(data.trainingSummary)
                updateTripEvent({
                    variables: {
                        input: {
                            id: +currentEvent?.id,
                            eventCategory: 'CrewTraining',
                            crewTrainingID: data.id,
                        },
                    },
                })
                closeModal()
            } else {
                console.error('mutationCreateTrainingSession error', response)
            }
        },
        onError: (error: any) => {
            console.error('mutationCreateTrainingSession error', error)
        },
    })
    const [
        mutationUpdateTrainingSession,
        { loading: mutationUpdateTrainingSessionLoading },
    ] = useMutation(UPDATE_TRAINING_SESSION, {
        onCompleted: (response: any) => {
            const data = response.updateTrainingSession
            if (data.id > 0) {
                updateTrainingSessionDues()
                updateSignatures(trainingID)
                handleEditorChange(data.trainingSummary)
            } else {
                console.error('mutationUpdateTrainingSession error', response)
            }
        },
        onError: (error: any) => {
            console.error('mutationUpdateTrainingSession error', error)
        },
    })
    const [
        readOneTrainingSessionDue,
        { loading: readOneTrainingSessionDueLoading },
    ] = useLazyQuery(READ_ONE_TRAINING_SESSION_DUE, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            return response.readOneTrainingSessionDue.data
        },
        onError: (error: any) => {
            console.error('readOneTrainingSessionDueLoading error:', error)
            return null
        },
    })
    const getTrainingSessionDueWithVariables = async (
        variables: any = {},
        onCompleted: any,
    ) => {
        if (offline) {
            const allDues = await trainingSessionDueModel.getAll()
            const data = allDues.filter(
                (item: any) =>
                    item.memberID === variables.filter.memberID.eq &&
                    item.vesselID === variables.filter.vesselID.eq &&
                    item.trainingTypeID === variables.filter.trainingTypeID.eq,
            )
            onCompleted(data)
        } else {
            const { data }: any = await readOneTrainingSessionDue({
                variables: variables,
            })
            onCompleted(data.readOneTrainingSessionDue)
        }
    }

    const [
        mutationCreateTrainingSessionDue,
        { loading: createTrainingSessionDueLoading },
    ] = useMutation(CREATE_TRAINING_SESSION_DUE, {
        onCompleted: (response: any) => {},
        onError: (error: any) => {
            console.error('createTrainingSessionDue error', error)
        },
    })
    const [
        mutationUpdateTrainingSessionDue,
        { loading: updateTrainingSessionDueLoading },
    ] = useMutation(UPDATE_TRAINING_SESSION_DUE, {
        onCompleted: (response: any) => {},
        onError: (error: any) => {
            console.error('updateTrainingSessionDue error', error)
        },
    })
    const updateTrainingSessionDues = async () => {
        const trainingSessionDues: any = []
        const vesselID = training.VesselID
        training.TrainingTypes.forEach((t: any) => {
            const trainingInfo = trainingTypes.find((tt: any) => tt.id === t)

            if (!isEmpty(trainingInfo) && trainingInfo.occursEvery > 0) {
                const trainingTypeID = t
                const newDueDate = dayjs(training.Date).add(
                    trainingInfo.occursEvery,
                    'day',
                )
                training.Members.forEach((m: any) => {
                    const memberID = m
                    trainingSessionDues.push({
                        dueDate: newDueDate.format('YYYY-MM-DD'),
                        memberID: memberID,
                        vesselID: vesselID,
                        trainingTypeID: trainingTypeID,
                    })
                })
            }
        })
        let trainingSessionDueWithIDs: any = []
        if (!isEmpty(trainingSessionDues)) {
            await Promise.all(
                trainingSessionDues.map(async (item: any) => {
                    const variables = {
                        filter: {
                            memberID: {
                                eq: item.memberID,
                            },
                            vesselID: {
                                eq: item.vesselID,
                            },
                            trainingTypeID: {
                                eq: item.trainingTypeID,
                            },
                        },
                    }
                    const onCompleted = (response: any) => {
                        trainingSessionDueWithIDs.push({
                            ...item,
                            id: response?.id ?? 0,
                        })
                    }

                    await getTrainingSessionDueWithVariables(
                        variables,
                        onCompleted,
                    )
                }),
            )
        }

        if (!isEmpty(trainingSessionDueWithIDs)) {
            await Promise.all(
                Array.from(trainingSessionDueWithIDs).map(async (item: any) => {
                    const variables = {
                        variables: { input: item },
                    }
                    if (item.id === 0) {
                        if (offline) {
                            // mutationCreateTrainingSessionDue
                            await trainingSessionDueModel.save({
                                ...item,
                                id: generateUniqueId(),
                            })
                        } else {
                            await mutationCreateTrainingSessionDue(variables)
                        }
                    } else {
                        if (offline) {
                            // mutationUpdateTrainingSessionDue
                            await trainingSessionDueModel.save(item)
                        } else {
                            await mutationUpdateTrainingSessionDue(variables)
                        }
                    }
                }),
            )
        }
    }

    const [updateTripEvent] = useMutation(UpdateTripEvent, {
        onCompleted: (response) => {
            getCurrentEvent(currentEvent?.id)
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
        },
        onError: (error) => {
            console.error('Error updating trip event', error)
        },
    })
    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            const data = response.createTripEvent
            setCurrentEvent(data)
            saveTraining()
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
        },
    })

    const saveTraining = async () => {
        const input = {
            id: trainingID,
            date: training.Date
                ? dayjs(logBookStartDate).format('YYYY-MM-DD')
                : '',
            members: training.Members?.join(','),
            trainerID: training.TrainerID,
            trainingSummary: content,
            trainingTypes: training.TrainingTypes?.join(','),
            vesselID: training?.VesselID,
            trainingLocationType: training?.VesselID
                ? training.VesselID === 'Other' ||
                  training.VesselID === 'Onshore'
                    ? training.VesselID
                    : 'Vessel'
                : 'Location',
            fuelLevel: `${training.FuelLevel}`,
            geoLocationID: training.GeoLocationID,
            startTime: startTime,
            finishTime: finishTime,
            lat: `${training.Lat}`,
            long: `${training.Long}`,
        }
        if (trainingID === 0) {
            if (offline) {
                // mutationCreateTrainingSession
                const data = await trainingSessionModel.save({
                    ...input,
                    id: generateUniqueId(),
                })

                setTrainingID(data.id)
                updateTrainingSessionDues()
                updateSignatures(data.id)
                handleEditorChange(data.trainingSummary)
                // updateTripEvent
                await tripEventModel.save({
                    id: +currentEvent?.id,
                    eventCategory: 'CrewTraining',
                    crewTrainingID: data.id,
                })

                await getCurrentEvent(currentEvent?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
                closeModal()
            } else {
                await mutationCreateTrainingSession({
                    variables: {
                        input: input,
                    },
                })
            }
        } else {
            if (offline) {
                // mutationUpdateTrainingSession
                const data = await trainingSessionModel.save(input)
                updateTrainingSessionDues()
                updateSignatures(trainingID)
                handleEditorChange(data.trainingSummary)
            } else {
                await mutationUpdateTrainingSession({
                    variables: {
                        input: input,
                    },
                })
            }
        }
    }
    const handleSave = async () => {
        let hasErrors = false
        let errors = {
            TrainingTypes: '',
            TrainerID: '',
            VesselID: '',
            Date: '',
        }
        setFormErrors(errors)
        // Validate Training Types - check if empty or undefined
        if (!training.TrainingTypes || isEmpty(training.TrainingTypes)) {
            hasErrors = true
            errors.TrainingTypes = 'Nature of training is required'

            // Clear any previous error state for this field
            setFormErrors((prevErrors) => ({
                ...prevErrors,
                TrainingTypes: 'Nature of training is required',
            }))
        } else {
            // Clear any previous error for this field when valid
            setFormErrors((prevErrors) => ({
                ...prevErrors,
                TrainingTypes: '',
            }))
        }
        if (!(training.TrainerID && training.TrainerID > 0)) {
            hasErrors = true
            errors.TrainerID = 'Trainer is required'
        }
        if (
            !training.VesselID &&
            !(training.TrainingLocationID && training.TrainingLocationID >= 0)
        ) {
            hasErrors = true
            errors.VesselID = 'Location is required'
        }

        if (typeof training.Date === 'undefined') {
            training.Date = dayjs(logBookStartDate).format('YYYY-MM-DD')
        }

        if (training.Date === null || !dayjs(training.Date).isValid()) {
            hasErrors = true
            errors.Date = 'The date is invalid'
        }
        if (hasErrors) {
            setHasFormErrors(true)
            setFormErrors(errors)
            return
        }
        if (currentEvent) {
            if (offline) {
                // updateTripEvent
                await tripEventModel.save({
                    id: +currentEvent.id,
                    eventCategory: 'CrewTraining',
                    logBookEntrySectionID: currentTrip.id,
                })

                await getCurrentEvent(currentEvent?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
            } else {
                updateTripEvent({
                    variables: {
                        input: {
                            id: +currentEvent.id,
                            eventCategory: 'CrewTraining',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
            saveTraining()
            closeModal()
        } else {
            if (offline) {
                // createTripEvent
                const tripEventData = await tripEventModel.save({
                    id: generateUniqueId(),
                    eventCategory: 'CrewTraining',
                    logBookEntrySectionID: currentTrip.id,
                })
                setCurrentEvent(tripEventData)
                saveTraining()
            } else {
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'CrewTraining',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
        }
    }

    const updateSignatures = (TrainingID: number) => {
        signatureMembers.length > 0 &&
            signatureMembers?.forEach((signature: any) => {
                checkAndSaveSignature(signature, TrainingID)
            })
    }

    const checkAndSaveSignature = async (
        signature: any,
        TrainingID: number,
    ) => {
        if (offline) {
            // queryGetMemberTrainingSignatures
            const allSignatures = await memberTraining_SignatureModel.getAll()
            const data = allSignatures.filter(
                (item: any) =>
                    item.memberID === signature.MemberID &&
                    item.trainingSessionID === TrainingID,
            )
            if (data.length > 0) {
                // mutationUpdateMemberTrainingSignature
                await memberTraining_SignatureModel.save({
                    id: data[0].id,
                    memberID: signature.MemberID,
                    signatureData: signature.SignatureData,
                    trainingSessionID: TrainingID,
                })
            } else {
                // mutationCreateMemberTrainingSignature
                await memberTraining_SignatureModel.save({
                    id: generateUniqueId(),
                    memberID: signature.MemberID,
                    signatureData: signature.SignatureData,
                    trainingSessionID: TrainingID,
                })
            }
        } else {
            await queryGetMemberTrainingSignatures({
                variables: {
                    filter: {
                        memberID: { eq: signature.MemberID },
                        trainingSessionID: { in: TrainingID },
                    },
                },
            })
                .then((response: any) => {
                    const data =
                        response.data.readMemberTraining_Signatures.nodes
                    if (data.length > 0) {
                        mutationUpdateMemberTrainingSignature({
                            variables: {
                                input: {
                                    id: data[0].id,
                                    memberID: signature.MemberID,
                                    signatureData: signature.SignatureData,
                                    trainingSessionID: TrainingID,
                                },
                            },
                        })
                    } else {
                        if (signature.SignatureData) {
                            mutationCreateMemberTrainingSignature({
                                variables: {
                                    input: {
                                        memberID: signature.MemberID,
                                        signatureData: signature.SignatureData,
                                        trainingSessionID: TrainingID,
                                    },
                                },
                            })
                        }
                    }
                })
                .catch((error: any) => {
                    console.error(
                        'mutationGetMemberTrainingSignatures error',
                        error,
                    )
                })
        }
    }

    const [queryGetMemberTrainingSignatures] = useLazyQuery(
        GET_MEMBER_TRAINING_SIGNATURES,
    )

    const [
        mutationUpdateMemberTrainingSignature,
        { loading: mutationUpdateMemberTrainingSignatureLoading },
    ] = useMutation(UPDATE_MEMBER_TRAINING_SIGNATURE, {
        onCompleted: (response: any) => {
            const data = response.updateMemberTraining_Signature
            if (data.id > 0) {
                // signatureCount++
                // if (signatureCount === signatureMembers.length) {
                // }
            } else {
                console.error(
                    'mutationUpdateMemberTrainingSignature error',
                    response,
                )
            }
        },
        onError: (error: any) => {
            console.error('mutationUpdateMemberTrainingSignature error', error)
        },
    })

    const [
        mutationCreateMemberTrainingSignature,
        { loading: mutationCreateMemberTrainingSignatureLoading },
    ] = useMutation(CREATE_MEMBER_TRAINING_SIGNATURE, {
        onCompleted: (response: any) => {
            const data = response.createMemberTraining_Signature
            if (data.id > 0) {
                // signatureCount++
                // if (signatureCount === signatureMembers.length) {
                // }
            } else {
                console.error(
                    'mutationCreateMemberTrainingSignature error',
                    response,
                )
            }
        },
        onError: (error: any) => {
            console.error('mutationCreateMemberTrainingSignature error', error)
        },
    })

    const handleTrainingDateChange = (date: any) => {
        setTrainingDate(new Date(date.toString()).toLocaleDateString())
        setTraining({
            ...training,
            Date: dayjs(date).format('YYYY-MM-DD'),
        })
    }
    const handleTrainerChange = (trainer: any) => {
        if (!trainer) return // Add early return if trainer is null

        // Check if trainer is an array (multiple selection) or a single object
        const trainerValue = Array.isArray(trainer)
            ? trainer.length > 0
                ? trainer[0].value
                : null
            : trainer.value

        if (!trainerValue) {
            return
        }

        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array
        const membersSet = new Set(training?.Members || [])
        membersSet.add(trainerValue)
        const members = Array.from(membersSet)

        setTraining({
            ...training,
            TrainerID: trainerValue,
            Members: members,
        })

        // Create a proper trainer object for the selectedMemberList
        const trainerObject = Array.isArray(trainer) ? trainer[0] : trainer

        setSelectedMemberList([...selectedMemberList, trainerObject])
        setSignatureMembers([
            ...signatureMembers,
            {
                MemberID: +trainerValue,
                SignatureData: null,
            },
        ])
    }
    const handleTrainingTypeChange = (trainingTypes: any) => {
        // Update training state with selected types
        const selectedTypes = !isEmpty(trainingTypes)
            ? trainingTypes.map((item: any) => item.value)
            : []

        setTraining({
            ...training,
            TrainingTypes: selectedTypes,
        })

        // Clear error message if valid selection is made
        if (!isEmpty(selectedTypes)) {
            setFormErrors((prevErrors) => ({
                ...prevErrors,
                TrainingTypes: '',
            }))
        }
    }

    const handleMemberChange = (members: any) => {
        // Ensure members is an array
        const membersArray = Array.isArray(members)
            ? members
            : [members].filter(Boolean)

        // Make sure we're filtering with valid member values
        const signatures = signatureMembers.filter((item: any) =>
            membersArray.some(
                (m: any) => m && m.value && +m.value === item.MemberID,
            ),
        )

        // Extract member values safely
        const memberValues = membersArray
            .filter((item: any) => item && item.value)
            .map((item: any) => item.value)

        setTraining({
            ...training,
            Members: memberValues,
            // Signatures: signatures,
        })
        setSelectedMemberList(membersArray)
        setSignatureMembers(signatures)
    }
    const onSignatureChanged = (
        signature: string,
        member?: string | undefined,
        memberId?: number | undefined,
    ) => {
        const index = signatureMembers.findIndex(
            (object) => object.MemberID === memberId,
        )
        const updatedMembers = [...signatureMembers]
        if (signature) {
            if (index !== -1) {
                if (signature.trim() === '') {
                    updatedMembers.splice(index, 1)
                } else {
                    updatedMembers[index].SignatureData = signature
                }
            } else {
                updatedMembers.push({
                    MemberID: memberId,
                    SignatureData: signature,
                })
            }
        } else {
            updatedMembers.splice(index, 1)
        }
        setSignatureMembers(updatedMembers)
    }

    if (!offline) {
        getTrainingTypeByID(trainingTypeId, setTraining)
    }

    const handleTrainingVesselChange = (vessel: any) => {
        setTraining({
            ...training,
            VesselID: vessel.value,
        })
    }

    const getCurrentEvent = async (id: any) => {
        if (offline) {
            // getTripEvent
            const event = await tripEventModel.getById(id)
            if (event) {
                setTrainingID(event.crewTrainingID)
            }
        } else {
            getTripEvent({
                variables: {
                    id: id,
                },
            })
        }
    }

    const [getTripEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setTrainingID(event.crewTraining.id)
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })
    const [queryTrainingSessionByID] = useLazyQuery(TRAINING_SESSION_BY_ID, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneTrainingSession
            if (data) {
                handleSetTraining(data)
            }
        },
        onError: (error: any) => {
            console.error('queryTrainingSession error', error)
        },
    })
    const loadTrainingSession = async () => {
        if (offline) {
            // queryTrainingSessionByID
            const data = await trainingSessionModel.getById(trainingID)
            if (data) {
                handleSetTraining(data)
            }
        } else {
            await queryTrainingSessionByID({
                variables: {
                    id: +trainingID,
                },
            })
        }
    }
    const handleStartTimeChange = (date: any) => {
        setStartTime(dayjs(date).format('HH:mm'))
    }
    const handleFinishTimeChange = (date: any) => {
        setFinishTime(dayjs(date).format('HH:mm'))
    }
    const displayField = (fieldName: string) => {
        const eventTypesConfig =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'EventType_LogBookComponent',
            )
        if (
            eventTypesConfig?.length > 0 &&
            eventTypesConfig[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }
    const handleLocationChange = (value: any) => {
        // If value is null or undefined, return early
        if (!value) return

        // Check if the value is from dropdown selection (has 'value' property)
        if (value.value) {
            // Handle location selected from dropdown
            setTraining({
                ...training,
                GeoLocationID: +value.value,
                Lat: null,
                Long: null,
            })
        } else if (
            value.latitude !== undefined &&
            value.longitude !== undefined
        ) {
            // Handle direct coordinates input
            setTraining({
                ...training,
                GeoLocationID: 0, // Reset geoLocationID when using direct coordinates
                Lat: value.latitude,
                Long: value.longitude,
            })
        }
    }
    const handleSetCurrentLocation = (value: any) => {
        setTraining({
            ...training,
            GeoLocationID: 0,
            Lat: value.latitude,
            Long: value.longitude,
        })
    }
    useEffect(() => {
        if (selectedEvent) {
            setCurrentEvent(selectedEvent)
            getCurrentEvent(selectedEvent?.id)
        }
        if (currentEvent) {
            getCurrentEvent(currentEvent?.id)
        }
    }, [selectedEvent, currentEvent])
    useEffect(() => {
        if (+trainingID > 0) {
            loadTrainingSession()
        }
    }, [trainingID])
    useEffect(() => {
        if (isEmpty(training)) {
            setTraining({
                ...training,
                VesselID: vesselId,
            })
        }
    }, [training])
    const offlineUseEffect = async () => {
        // getTrainingTypeByID(trainingTypeId, setTraining)
        const training = await trainingTypeModel.getById(trainingTypeId)
        setTraining(training)
        // getTrainingTypes(setTrainingTypes)
        const types = await trainingTypeModel.getAll()
        setTrainingTypes(types)
    }
    useEffect(() => {
        if (offline) {
            offlineUseEffect()
        }
    }, [offline])

    const [createCustomisedComponentFieldData] = useMutation(
        CREATE_CUSTOMISED_COMPONENT_FIELD_DATA,
        {
            onCompleted: (response: any) => {
                const data = response.createCustomisedComponentFieldData
                if (data.id > 0 && rawTraining?.procedureFields?.nodes) {
                    setRawTraining({
                        ...rawTraining,
                        procedureFields: {
                            ...rawTraining.procedureFields,
                            nodes: [...rawTraining.procedureFields.nodes, data],
                        },
                    })
                } else {
                    console.error(
                        'createCustomisedComponentFieldData error',
                        response,
                    )
                }
            },
            onError: (error: any) => {
                console.error('createCustomisedComponentFieldData error', error)
            },
        },
    )

    const [updateCustomisedComponentFieldData] = useMutation(
        UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA,
        {
            onCompleted: (response: any) => {
                const data = response.updateCustomisedComponentFieldData
                if (data.id > 0) {
                    setRawTraining({
                        ...rawTraining,
                        procedureFields: {
                            ...rawTraining.procedureFields,
                            nodes: [
                                ...rawTraining?.procedureFields?.nodes.filter(
                                    (procedureField: any) =>
                                        procedureField.customisedComponentFieldID !==
                                        data.customisedComponentFieldID,
                                ),
                                {
                                    ...data,
                                },
                            ],
                        },
                    })
                } else {
                    console.error(
                        'updateCustomisedComponentFieldData error',
                        response,
                    )
                }
            },
            onError: (error: any) => {
                console.error('updateCustomisedComponentFieldData error', error)
            },
        },
    )

    const getProcedures = () => {
        const procedures = trainingTypes.filter((type: any) =>
            training?.TrainingTypes?.includes(type.id),
        )
        return procedures
            .map((type: any) => {
                return type.customisedComponentField.nodes.length > 0
                    ? {
                          id: type.id,
                          title: type.title,
                          fields: type.customisedComponentField.nodes,
                      }
                    : null
            })
            .filter((type: any) => type != null)
    }

    const handleProcedureChecks = (field: any, type: any, status: boolean) => {
        if (!trainingID) {
            const procedureCheck = bufferProcedureCheck.filter(
                (procedureField: any) => procedureField.fieldId !== field.id,
            )
            setBufferProcedureCheck([
                ...procedureCheck,
                { fieldId: field.id, status: status },
            ])
            return
        }
        const nodes = rawTraining?.procedureFields?.nodes
        const existingField = nodes?.find(
            (procedureField: any) =>
                procedureField.customisedComponentFieldID === field.id,
        )
        if (!nodes || !existingField) {
            createCustomisedComponentFieldData({
                variables: {
                    input: {
                        status: status ? 'Ok' : 'Not_Ok',
                        trainingSessionID: trainingID,
                        customisedComponentFieldID: field.id,
                    },
                },
            })
            return
        }

        if (nodes.length > 0 && existingField) {
            const fieldID = existingField.id
            updateCustomisedComponentFieldData({
                variables: {
                    input: {
                        id: +fieldID,
                        status: status ? 'Ok' : 'Not_Ok',
                        trainingSessionID: trainingID,
                        customisedComponentFieldID: field.id,
                    },
                },
            })
        } else {
            createCustomisedComponentFieldData({
                variables: {
                    input: {
                        status: status ? 'Ok' : 'Not_Ok',
                        trainingSessionID: trainingID,
                        customisedComponentFieldID: field.id,
                    },
                },
            })
        }
    }

    const getFieldStatus = (field: any) => {
        if (bufferProcedureCheck.length > 0) {
            const fieldStatus = bufferProcedureCheck.find(
                (procedureField: any) => procedureField.fieldId == field.id,
            )
            if (fieldStatus) {
                return fieldStatus.status ? 'Ok' : 'Not_Ok'
            }
        }
        const fieldStatus = rawTraining?.procedureFields?.nodes?.find(
            (procedureField: any) =>
                procedureField.customisedComponentFieldID == field.id,
        )
        return fieldStatus?.status || ''
    }

    const showCommentPopup = (field: any) => {
        const fieldComment = rawTraining?.procedureFields?.nodes?.find(
            (procedureField: any) =>
                procedureField.customisedComponentFieldID == field.id,
        )
        if (bufferFieldComment.length > 0) {
            const fieldComment = bufferFieldComment.find(
                (procedureField: any) => procedureField.fieldId == field.id,
            )
            setCurrentComment(fieldComment?.comment || '')
        } else {
            setCurrentComment(fieldComment?.comment || '')
        }
        setCurrentField(field)
        setCurrentFieldComment(fieldComment)
        setOpenCommentAlert(true)
    }

    const getComment = (field: any) => {
        if (bufferFieldComment.length > 0) {
            const fieldComment = bufferFieldComment.find(
                (procedureField: any) => procedureField.fieldId == field.id,
            )
            if (fieldComment) {
                return fieldComment.comment
            }
        }
        const fieldComment = rawTraining?.procedureFields?.nodes?.find(
            (procedureField: any) =>
                procedureField.customisedComponentFieldID == field.id,
        )
        return fieldComment?.comment || field.comment
    }

    const handleSaveComment = () => {
        if (!trainingID) {
            const fieldComment = bufferFieldComment.filter(
                (procedureField: any) =>
                    procedureField.fieldId !== currentField.id,
            )
            setBufferFieldComment([
                ...fieldComment,
                { fieldId: currentField.id, comment: currentComment },
            ])
            setOpenCommentAlert(false)
            return
        }
        if (currentFieldComment) {
            updateCustomisedComponentFieldData({
                variables: {
                    input: {
                        id: currentFieldComment.id,
                        trainingSessionID: trainingID,
                        customisedComponentFieldID: currentField.id,
                        comment: currentComment,
                    },
                },
            })

            const nodes = rawTraining?.procedureFields?.nodes
            if (nodes) {
                setRawTraining({
                    ...rawTraining,
                    procedureFields: {
                        ...rawTraining.procedureFields,
                        nodes: [
                            ...nodes.filter(
                                (procedureField: any) =>
                                    procedureField.customisedComponentFieldID !==
                                    currentField.id,
                            ),
                            {
                                ...currentFieldComment,
                                comment: currentComment,
                            },
                        ],
                    },
                })
            }
        } else {
            createCustomisedComponentFieldData({
                variables: {
                    input: {
                        trainingSessionID: trainingID,
                        customisedComponentFieldID: currentField.id,
                        comment: currentComment,
                    },
                },
            })
        }
        setOpenCommentAlert(false)
    }

    return (
        <div className="space-y-6">
            {!training && trainingID > 0 ? (
                <TrainingSessionFormSkeleton />
            ) : (
                <div className="space-y-6">
                    <Label label="Name of trainer" disabled={locked}>
                        <CrewDropdown
                            disabled={locked}
                            offline={offline}
                            value={training?.TrainerID}
                            onChange={handleTrainerChange}
                            memberIdOptions={memberIdOptions}
                        />
                        <small className="text-destructive">
                            {hasFormErrors && formErrors.TrainerID}
                        </small>
                    </Label>
                    <Label label="Crew trained" disabled={locked}>
                        <CrewMultiSelectDropdown
                            offline={offline}
                            value={training?.Members}
                            onChange={handleMemberChange}
                            memberIdOptions={memberIdOptions}
                        />
                    </Label>

                    <Label label="Training types" disabled={locked}>
                        <div className="flex flex-col space-y-6 w-full">
                            <TrainingTypeMultiSelectDropdown
                                offline={offline}
                                value={training?.TrainingTypes}
                                onChange={handleTrainingTypeChange}
                                locked={locked}
                            />
                            {formErrors.TrainingTypes && (
                                <small className="text-destructive mt-1">
                                    {formErrors.TrainingTypes}
                                </small>
                            )}

                            {training &&
                                trainingTypes.filter(
                                    (type: any) =>
                                        training?.TrainingTypes?.includes(
                                            type.id,
                                        ) && type.procedure,
                                ).length > 0 && (
                                    <Button
                                        variant="primary"
                                        className="w-fit flex-1"
                                        onClick={() =>
                                            setOpenViewProcedure(true)
                                        }>
                                        View Procedures
                                    </Button>
                                )}
                        </div>
                    </Label>
                    <Label label="Location of training" disabled={locked}>
                        {vesselList && (rawTraining || trainingID === 0) && (
                            <Combobox
                                options={vesselList || []}
                                value={
                                    rawTraining?.trainingLocationType ===
                                    'Vessel'
                                        ? vesselList?.filter(
                                              (vessel: any) =>
                                                  +vessel.value ===
                                                  +training?.VesselID,
                                          )[0]
                                        : rawTraining?.trainingLocationType ===
                                            'Onshore'
                                          ? {
                                                label: 'Desktop/shore',
                                                value: rawTraining?.trainingLocationType,
                                            }
                                          : rawTraining?.trainingLocationType ===
                                              'Other'
                                            ? {
                                                  label: 'Other',
                                                  value: rawTraining?.trainingLocationType,
                                              }
                                            : rawTraining?.trainingLocationType ===
                                                    'Location' &&
                                                rawTraining?.trainingLocation
                                                    ?.id > 0
                                              ? {
                                                    label: rawTraining
                                                        ?.trainingLocation
                                                        ?.title,
                                                    value: rawTraining
                                                        ?.trainingLocation?.id,
                                                }
                                              : vesselList?.find(
                                                    (vessel: any) =>
                                                        +vessel.value ===
                                                        +vesselId,
                                                ) || null
                                }
                                onChange={handleTrainingVesselChange}
                                placeholder="Select location"
                                isDisabled={true}
                                buttonClassName="w-full"
                            />
                        )}
                    </Label>
                    <small className="text-destructive">
                        {hasFormErrors && formErrors.VesselID}
                    </small>

                    {displayField('CrewTraining_FuelLevel') && (
                        <Label
                            label="Fuel level at end of training"
                            disabled={locked}>
                            <Input
                                id="fuel-level"
                                name="fuel-level"
                                type="number"
                                defaultValue={training?.FuelLevel}
                                className={`w-full`}
                                placeholder="Fuel level"
                                onChange={debounce(function (e) {
                                    setTraining({
                                        ...training,
                                        FuelLevel: e.target.value,
                                    })
                                }, 600)}
                            />
                        </Label>
                    )}
                    {displayField('CrewTraining_StartTime') ||
                        (displayField('CrewTraining_FinishTime') && (
                            <Label
                                label="Training duration"
                                className="mb-1"
                                disabled={locked}
                            />
                        ))}

                    {displayField('CrewTraining_StartTime') && (
                        <Label label="Start time of training" disabled={locked}>
                            <TimeField
                                time={startTime}
                                handleTimeChange={handleStartTimeChange}
                                timeID="startTime"
                                fieldName="Time"
                            />
                        </Label>
                    )}

                    {displayField('CrewTraining_FinishTime') && (
                        <Label label="End time of training" disabled={locked}>
                            <TimeField
                                time={finishTime}
                                handleTimeChange={handleFinishTimeChange}
                                timeID="finishTime"
                                fieldName="Time"
                            />
                        </Label>
                    )}

                    {(!currentEvent || training) && (
                        <Editor
                            id="TrainingSummary"
                            placeholder="Summary of training, identify any outcomes, further training required or other observations."
                            handleEditorChange={handleEditorChange}
                            content={content}
                            disabled={locked}
                        />
                    )}
                    {/*<div className="w-full my-4 flex flex-col">
                        <textarea
                            id="TrainingSummary"
                            name="TrainingSummary"
                            placeholder="Summary of training, identify any outcomes, further training required or other observations."
                            defaultValue={training?.TrainingSummary}
                            rows={4}
                            className={''}></textarea>
                    </div>*/}
                    {selectedMemberList.length > 0 && (
                        <>
                            <H4>Participant Signatures</H4>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                {selectedMemberList.map(
                                    (member: any, index: number) => (
                                        <SignaturePad
                                            key={index}
                                            title={member.label}
                                            member={member.label}
                                            memberId={member.value}
                                            onSignatureChanged={
                                                onSignatureChanged
                                            }
                                            signature={{
                                                signatureData:
                                                    signatureMembers.find(
                                                        (sig: any) =>
                                                            sig.MemberID ===
                                                            member.value,
                                                    )?.SignatureData,
                                                id: signatureMembers.find(
                                                    (sig: any) =>
                                                        sig.MemberID ===
                                                        member.value,
                                                )?.ID,
                                            }}
                                            locked={locked}
                                        />
                                    ),
                                )}
                            </div>
                        </>
                    )}
                    <div className="flex justify-end pb-4 pt-4 gap-2">
                        <Button
                            variant="back"
                            iconLeft={ArrowLeft}
                            onClick={closeModal}>
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            iconLeft={Check}
                            onClick={locked ? () => {} : handleSave}
                            disabled={
                                mutationCreateTrainingSessionLoading ||
                                mutationUpdateTrainingSessionLoading
                            }>
                            {trainingID === 0 ? 'Save' : 'Update'}
                        </Button>
                    </div>
                </div>
            )}

            <Sheet open={openViewProcedure} onOpenChange={setOpenViewProcedure}>
                <SheetContent side="right" className="w-3/4 sm:max-w-md">
                    <SheetHeader>
                        <SheetTitle>Procedures</SheetTitle>
                    </SheetHeader>
                    <SheetBody>
                        {training &&
                            trainingTypes
                                .filter(
                                    (type: any) =>
                                        training?.TrainingTypes?.includes(
                                            type.id,
                                        ) && type.procedure,
                                )
                                .map((type: any) => (
                                    <div
                                        key={type.id}
                                        className="border rounded-md p-4 mb-4">
                                        <h3 className="font-medium leading-6 mb-4">
                                            {type.title}
                                        </h3>
                                        <div
                                            key={type.id}
                                            dangerouslySetInnerHTML={{
                                                __html: type.procedure,
                                            }}></div>
                                    </div>
                                ))}
                    </SheetBody>
                </SheetContent>
            </Sheet>
        </div>
    )
}

export default CrewTrainingEvent
