// table.tsx
import * as React from 'react'
import { cn } from '@/app/lib/utils'

export const Table = React.forwardRef<
    HTMLTableElement,
    React.HTMLAttributes<HTMLTableElement>
>(({ className, ...props }, ref) => (
    <div className={cn('relative w-full overflow-auto', className)}>
        <table
            ref={ref}
            cellSpacing={0}
            className="w-full caption-bottom border-spacing-0"
            {...props}
        />
    </div>
))
Table.displayName = 'Table'

export const TableHeader = React.forwardRef<
    HTMLTableSectionElement,
    React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
    <thead
        ref={ref}
        className={cn('[&_tr]:border-border', className)}
        {...props}
    />
))
TableHeader.displayName = 'TableHeader'

export const TableBody = React.forwardRef<
    HTMLTableSectionElement,
    React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, children, ...props }, ref) => (
    <tbody
        ref={ref}
        className={cn('[&_tr:last-child]:border-0', className)}
        {...props}>
        {children}
    </tbody>
))
TableBody.displayName = 'TableBody'

export const TableFooter = React.forwardRef<
    HTMLTableSectionElement,
    React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
    <tfoot
        ref={ref}
        className={cn(
            'border-t border-border bg-background/50 font-medium [&>tr]:last:border-b-0',
            className,
        )}
        {...props}
    />
))
TableFooter.displayName = 'TableFooter'

interface TableRowProps extends React.HTMLAttributes<HTMLTableRowElement> {
    noHoverEffect?: boolean
    statusOverlayColor?: 'destructive' | 'warning'
}

export const TableRow = React.forwardRef<HTMLTableRowElement, TableRowProps>(
    (
        { className, noHoverEffect = false, statusOverlayColor, ...props },
        ref,
    ) => (
        <tr
            ref={ref}
            className={cn(
                /* base row styling */
                'cursor-pointer border-b border-wedgewood-100 dark:border-wedgewood-900 group data-[state=selected]:bg-accent',

                /* ✅ Safari-compatible hover effect using background gradient animation */
                !noHoverEffect && [
                    'bg-gradient-to-r from-accent to-accent bg-no-repeat',
                    'bg-[length:0%_100%] hover:bg-[length:100%_100%]',
                    'transition-[background-size] duration-300 ease-out',
                    statusOverlayColor === 'destructive' &&
                        'from-cinnabar-100 to-cinnabar-100',
                    statusOverlayColor === 'warning' &&
                        'from-fire-bush-100 to-fire-bush-100',
                ],

                /* Focus styles for accessibility */
                'focus-within:outline focus-within:outline-2 focus-within:outline-accent focus-within:outline-offset-2',

                className,
            )}
            {...props}
        />
    ),
)
TableRow.displayName = 'TableRow'

export const TableHead = React.forwardRef<
    HTMLTableCellElement,
    React.ThHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
    <th
        ref={ref}
        className={cn(
            'h-8 px-0 small:px-1 phablet:px-[7px] pb-2 small:p-auto cursor-default relative font-normal [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-fit whitespace-nowrap',
            className,
        )}
        {...props}
    />
))
TableHead.displayName = 'TableHead'

interface TableCellProps extends React.TdHTMLAttributes<HTMLTableCellElement> {
    statusOverlay?: boolean
    statusOverlayColor?: 'destructive' | 'warning'
    dataIndex?: number
}

export const TableCell = React.forwardRef<HTMLTableCellElement, TableCellProps>(
    (
        { className, statusOverlay = false, statusOverlayColor, ...props },
        ref,
    ) => (
        <td
            ref={ref}
            className={cn(
                /* ✅ Lift cell contents above the row overlay with relative z-10 */
                'relative z-10 h-20 font-normal align-center text-card-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] tiny:first:pl-1 tiny:last:pr-1 small:first:pl-1.5 small:last:pr-1.5 phablet:first:pl-2.5 phablet:last:pr-2.5',
                statusOverlayColor === 'destructive' &&
                    'group-hover:text-outer-space-800',
                statusOverlayColor === 'warning' &&
                    'group-hover:text-outer-space-800',
                className,
            )}
            {...props}>
            {props.children}
        </td>
    ),
)
TableCell.displayName = 'TableCell'

export const TableCaption = React.forwardRef<
    HTMLTableCaptionElement,
    React.HTMLAttributes<HTMLTableCaptionElement>
>(({ className, ...props }, ref) => (
    <caption
        ref={ref}
        className={cn('mt-4 text-muted-foreground', className)}
        {...props}
    />
))
TableCaption.displayName = 'TableCaption'
