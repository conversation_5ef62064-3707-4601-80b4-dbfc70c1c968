'use client'
import React, { useEffect, useState } from 'react'
import {
    CreateSupernumerary_LogBookEntrySection,
    UpdateSupernumerary_LogBookEntrySection,
    UpdateLogBookEntrySection_Signature,
    CreateLogBookEntrySection_Signature,
    CreateTripEvent,
    UpdateTripEvent,
    CreateEventType_Supernumerary,
    UpdateEventType_Supernumerary,
} from '@/app/lib/graphQL/mutation'
import {
    Table,
    TableHeader,
    TableRow,
    TableHead,
    TableBody,
    TableCell,
} from '@/components/ui/table'
import { useLazyQuery, useMutation } from '@apollo/client'
import { useSearchParams } from 'next/navigation'
import {
    // GetTripEvent, // Commented out as it's not currently used
    ReadOneEventType_Supernumerary,
    Supernumerary_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
import { Check } from 'lucide-react'
import { Button } from '@/components/ui/button'
import SignaturePadComponent from '@/components/signature-pad'

import dayjs from 'dayjs'
import { isEmpty, trim } from 'lodash'
import TimeField from '../components/time'
import TripEventModel from '@/app/offline/models/tripEvent'
import EventType_SupernumeraryModel from '@/app/offline/models/eventType_Supernumerary'
import Supernumerary_LogBookEntrySectionModel from '@/app/offline/models/supernumerary_LogBookEntrySection'
import LogBookEntrySection_SignatureModel from '@/app/offline/models/logBookEntrySection_Signature'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { H4, P } from '@/components/ui/typography'
import {
    CheckField,
    CheckFieldContent,
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'
// Removed unused imports

export default function SupernumeraryEvent({
    logBookConfig = false,
    locked,
    closeModal,
    currentTrip = false,
    updateTripReport,
    tripReport,
    inLogbook = false,
    selectedEvent = false,
    offline = false,
}: {
    inLogbook?: boolean
    logBookConfig: any
    locked: boolean
    closeModal: any
    currentTrip: any
    updateTripReport: any
    tripReport: any
    selectedEvent: any
    offline?: boolean
}) {
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const [currentSignature, setCurrentSignature] = useState<any>(null)
    const [currentGuest, setCurrentGuest] = useState<any>(false)
    const [supernumeraryConfig, setSupernumeraryConfig] = useState<any>(false)
    const [openAddGuestDialog, setOpenAddGuestDialog] = useState<any>(false)
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const [supernumerary, setSupernumerary] = useState({} as any)
    const [formError, setFormError] = useState({} as any)
    const [closeOnSave, setCloseOnSave] = useState(false)

    const tripEventModel = new TripEventModel()
    const supernumeraryModel = new EventType_SupernumeraryModel()
    const supernumerarySectionModel =
        new Supernumerary_LogBookEntrySectionModel()
    const signatureModel = new LogBookEntrySection_SignatureModel()

    const handleSaveGuest = async () => {
        if (
            !supernumeraryConfig ||
            supernumeraryConfig.find(
                (c: any) =>
                    c.title === 'Supernumerary_Signature' && c.status != 'Off',
            )
        ) {
            if (currentSignature?.id > 0) {
                if (offline) {
                    // updateLogBookEntrySection_Signature
                    await signatureModel.save({
                        id: currentSignature?.id,
                        signatureData: currentSignature.signatureData,
                    })
                } else {
                    updateLogBookEntrySection_Signature({
                        variables: {
                            input: {
                                id: currentSignature?.id,
                                signatureData: currentSignature.signatureData,
                            },
                        },
                    })
                }
                updateGuest(currentSignature?.id)
            } else {
                if (currentSignature) {
                    if (offline) {
                        // createLogBookEntrySection_Signature
                        const data = await signatureModel.save({
                            id: generateUniqueId(),
                            signatureData: currentSignature.signatureData,
                        })
                        updateGuest(data.id)
                        setOpenAddGuestDialog(false)
                    } else {
                        createLogBookEntrySection_Signature({
                            variables: {
                                input: {
                                    signatureData:
                                        currentSignature.signatureData,
                                },
                            },
                        })
                    }
                } else {
                    updateGuest()
                }
            }
        } else {
            updateGuest()
        }
    }

    const handleDeleteGuest = async () => {
        if (offline) {
            // updateSupernumeraryLogbookEntrySection
            await supernumerarySectionModel.save({
                id: currentGuest.id,
                supernumeraryID: 0,
            })
            setCurrentSignature(false)
            // getSectionSupernumerary_LogBookEntrySection
            await supernumerarySectionModel.getByIds(
                supernumerary.guestList.nodes.map((guest: any) => guest.id),
            )
            setCurrentGuest(false)
            // readOneEventType_Supernumerary
            const data = await supernumeraryModel.getById(supernumerary.id)
            setSupernumerary(data)
            setOpenAddGuestDialog(false)
        } else {
            updateSupernumeraryLogbookEntrySection({
                variables: {
                    input: {
                        id: currentGuest.id,
                        supernumeraryID: 0,
                    },
                },
            })
        }
    }

    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            const data = response.createTripEvent
            setCurrentEvent(data)
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
        },
    })

    const [createSupernumerary, { loading: createSupernumeraryLoading }] =
        useMutation(CreateEventType_Supernumerary, {
            onCompleted: (data) => {
                const supernumeraryID = data.createEventType_Supernumerary.id
                setSupernumerary({
                    ...supernumerary,
                    id: supernumeraryID,
                })
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'EventSupernumerary',
                            logBookEntrySectionID: currentTrip.id,
                            supernumeraryID: supernumeraryID,
                        },
                    },
                })
                if (closeOnSave) {
                    setCloseOnSave(false)
                    closeModal()
                }
            },
            onError: (error) => {
                console.error('Error creating supernumerary event', error)
            },
        })
    const [createSupernumeraryLogbookEntrySection] = useMutation(
        CreateSupernumerary_LogBookEntrySection,
        {
            onCompleted: () => {
                loadSupernumerary(supernumerary.id)
                setCurrentSignature(false)
                setOpenAddGuestDialog(false)
            },

            onError: (error) => {
                console.error(error)
            },
        },
    )

    // This function is not currently used but may be needed in the future
    // for retrieving event data
    // const getCurrentEvent = async (id: any) => {
    //     if (offline) {
    //         // getTripEvent
    //         const event = await tripEventModel.getById(id)
    //         if (event) {
    //             // setTrainingID(event.crewTraining.id)
    //             setCurrentEvent(event)
    //         }
    //     } else {
    //         getTripEvent({
    //             variables: {
    //                 id: id,
    //             },
    //         })
    //     }
    // }

    // This query is not currently used but may be needed if getCurrentEvent is used
    // const [getTripEvent] = useLazyQuery(GetTripEvent, {
    //     fetchPolicy: 'cache-and-network',
    //     onCompleted: (response) => {
    //         const event = response.readOneTripEvent
    //         if (event) {
    //             // setTrainingID(event.crewTraining.id)
    //         }
    //     },
    //     onError: (error) => {
    //         console.error('Error getting current event', error)
    //     },
    // })
    const [updateTripEvent] = useMutation(UpdateTripEvent, {
        onCompleted: () => {
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
        },
        onError: (error) => {
            console.error('Error updating trip event', error)
        },
    })

    const [updateSupernumerary, { loading: updateSupernumeraryLoading }] =
        useMutation(UpdateEventType_Supernumerary, {
            onCompleted: () => {
                if (closeOnSave) {
                    updateTripReport({
                        id: [
                            ...tripReport.map((trip: any) => trip.id),
                            currentTrip.id,
                        ],
                    })
                    setCloseOnSave(false)
                    closeModal()
                }
            },
            onError: (error) => {
                console.error('Error updating supernumerary event', error)
            },
        })
    const [updateSupernumeraryLogbookEntrySection] = useMutation(
        UpdateSupernumerary_LogBookEntrySection,
        {
            onCompleted: () => {
                setCurrentSignature(false)
                getSectionSupernumerary_LogBookEntrySection({
                    variables: {
                        id: supernumerary.guestList.nodes.map(
                            (guest: any) => guest.id,
                        ),
                    },
                })
                setOpenAddGuestDialog(false)
            },

            onError: (error) => {
                console.error(error)
            },
        },
    )

    const [createLogBookEntrySection_Signature] = useMutation(
        CreateLogBookEntrySection_Signature,
        {
            onCompleted: (data) => {
                updateGuest(data?.createLogBookEntrySection_Signature?.id)
                setOpenAddGuestDialog(false)
            },

            onError: (error) => {
                console.error(error)
            },
        },
    )

    const updateGuest = async (signatureID = 0) => {
        const firstName = (
            document.getElementById('firstname') as HTMLInputElement
        )?.value
        const surname = (document.getElementById('surname') as HTMLInputElement)
            ?.value
        if (currentGuest && currentGuest?.id > 0) {
            if (signatureID == 0) {
                signatureID = currentGuest.sectionSignature.id
            }
            if (offline) {
                // updateSupernumeraryLogbookEntrySection
                await supernumerarySectionModel.save({
                    id: currentGuest.id,
                    firstName: firstName,
                    surname: surname,
                    sectionSignatureID: signatureID,
                    supernumeraryID: supernumerary.id,
                })
                setCurrentSignature(false)
                // getSectionSupernumerary_LogBookEntrySection
                await supernumerarySectionModel.getByIds(
                    supernumerary.guestList.nodes.map((guest: any) => guest.id),
                )
                setCurrentGuest(false)
                // readOneEventType_Supernumerary
                const data = await supernumeraryModel.getById(supernumerary.id)
                setSupernumerary(data)
                setOpenAddGuestDialog(false)
            } else {
                updateSupernumeraryLogbookEntrySection({
                    variables: {
                        input: {
                            id: currentGuest.id,
                            firstName: firstName,
                            surname: surname,
                            sectionSignatureID: signatureID,
                            supernumeraryID: supernumerary.id,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                // createSupernumeraryLogbookEntrySection
                await supernumerarySectionModel.save({
                    id: generateUniqueId(),
                    firstName: firstName,
                    surname: surname,
                    logBookEntryID: logentryID,
                    sectionSignatureID: signatureID,
                    supernumeraryID: supernumerary.id,
                })
                loadSupernumerary(supernumerary.id)
                setCurrentSignature(false)
                setOpenAddGuestDialog(false)
            } else {
                if (supernumerary.id > 0) {
                    createSupernumeraryLogbookEntrySection({
                        variables: {
                            input: {
                                firstName: firstName,
                                surname: surname,
                                logBookEntryID: logentryID,
                                sectionSignatureID: signatureID,
                                supernumeraryID: supernumerary.id,
                            },
                        },
                    })
                }
            }
        }
    }

    const [updateLogBookEntrySection_Signature] = useMutation(
        UpdateLogBookEntrySection_Signature,
        {
            onCompleted: () => {},

            onError: (error) => {
                console.error(error)
            },
        },
    )

    const onSignatureChanged = (sign: string) => {
        currentSignature
            ? setCurrentSignature({ ...currentSignature, signatureData: sign })
            : setCurrentSignature({ signatureData: sign })
    }

    const [getSectionSupernumerary_LogBookEntrySection] = useLazyQuery(
        Supernumerary_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: () => {
                // We don't need to use the response data here
                setCurrentGuest(false)
                readOneEventType_Supernumerary({
                    variables: {
                        id: supernumerary.id,
                    },
                })
            },
            onError: (error: any) => {
                console.error('Supernumerary_LogBookEntrySection error', error)
            },
        },
    )

    // Using handleSaveGuest and setOpenAddGuestDialog(false) directly instead

    const handleAddGuest = () => {
        setCurrentGuest(false)
        setOpenAddGuestDialog(true)
    }

    const handleFirstNameClick = (guest: any) => {
        setCurrentGuest(guest)
        setOpenAddGuestDialog(true)
    }
    const handleSave = async () => {
        const input = {
            id: supernumerary.id || 0,
            title: supernumerary.title,
            totalGuest: +supernumerary.totalGuest || 0,
            isBriefed: supernumerary.isBriefed || false,
            briefingTime: supernumerary.briefingTime || dayjs().format('HH:mm'),
        }
        setFormError({})
        if (isEmpty(supernumerary)) {
            setFormError({
                element: 'title',
                message: 'Title is required',
            })
            return
        } else if (!supernumerary.title || isEmpty(trim(supernumerary.title))) {
            setFormError({
                element: 'title',
                message: 'Title is required',
            })
        } else if (
            !supernumerary.totalGuest ||
            +supernumerary.totalGuest <= 0
        ) {
            setFormError({
                element: 'totalGuest',
                message: 'Number of guests is required',
            })
            return
        }

        if (currentEvent?.supernumerary) {
            if (offline) {
                // updateTripEvent
                const data = await tripEventModel.save({
                    id: +currentEvent.id,
                    eventCategory: 'EventSupernumerary',
                    logBookEntrySectionID: currentTrip.id,
                })
                setCurrentEvent(data)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
            } else {
                updateTripEvent({
                    variables: {
                        input: {
                            id: +currentEvent.id,
                            eventCategory: 'EventSupernumerary',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
        }

        if (!supernumerary.id || +supernumerary.id === 0) {
            if (offline) {
                const data = await supernumeraryModel.save({
                    ...input,
                    id: generateUniqueId(),
                })
                const supernumeraryID = data.id
                setSupernumerary(data)
                // createTripEvent
                const tripEventData = await tripEventModel.save({
                    id: generateUniqueId(),
                    eventCategory: 'EventSupernumerary',
                    logBookEntrySectionID: currentTrip.id,
                    supernumeraryID: supernumeraryID,
                })
                setCurrentEvent(tripEventData)
                if (closeOnSave) {
                    setCloseOnSave(false)
                    closeModal()
                }
            } else {
                createSupernumerary({
                    variables: {
                        input: input,
                    },
                })
            }
        } else {
            if (offline) {
                const data = await supernumeraryModel.save(input)
                setSupernumerary(data)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
                if (closeOnSave) {
                    setCloseOnSave(false)
                    closeModal()
                }
            } else {
                updateSupernumerary({
                    variables: {
                        input: input,
                    },
                })
            }
        }
    }
    const handleBriefingTimeChange = (date: any) => {
        const briefingTime = dayjs(date).format('HH:mm')
        setSupernumerary({
            ...supernumerary,
            briefingTime: briefingTime,
        })
    }
    const [readOneEventType_Supernumerary, { loading: loadingSupernumerary }] =
        useLazyQuery(ReadOneEventType_Supernumerary, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (data) => {
                setSupernumerary(data.readOneEventType_Supernumerary)
            },
            onError: (error) => {
                console.error(error)
            },
        })
    const loadSupernumerary = async (id: number) => {
        if (offline) {
            // readOneEventType_Supernumerary
            const data = await supernumeraryModel.getById(id)
            setSupernumerary(data)
        } else {
            await readOneEventType_Supernumerary({
                variables: {
                    id: id,
                },
            })
        }
    }
    useEffect(() => {
        if (logBookConfig) {
            setSupernumeraryConfig(
                logBookConfig.customisedLogBookComponents.nodes
                    .filter(
                        (config: any) =>
                            config.componentClass ===
                            'EventType_LogBookComponent',
                    )[0]
                    ?.customisedComponentFields.nodes.map((field: any) => ({
                        title: field.fieldName,
                        status: field.status,
                    })),
            )
        }
        if (currentEvent?.supernumerary) {
            loadSupernumerary(currentEvent.supernumerary.id)
        }
    }, [logBookConfig, currentEvent])
    return (
        <div className="space-y-8">
            <div>
                {!inLogbook && (
                    <H4 className="mt-6 uppercase">Supernumerary</H4>
                )}
                <P className="max-w-[40rem] leading-loose">
                    This section covers guest sign-ins and any policies they
                    must read.
                </P>
            </div>
            {supernumerary?.id && +supernumerary?.id > 0 && (
                <Button
                    onClick={handleAddGuest}
                    isLoading={
                        loadingSupernumerary ||
                        createSupernumeraryLoading ||
                        updateSupernumeraryLoading
                    }
                    iconLeft={Check}>
                    Add Guest
                </Button>
            )}
            <div className="space-y-8">
                <div>
                    <Label label="Title of supernumerary" htmlFor="title">
                        <Input
                            id="title"
                            type="text"
                            value={supernumerary?.title}
                            onChange={(e) => {
                                setSupernumerary({
                                    ...supernumerary,
                                    title: e.target.value,
                                })
                            }}
                            placeholder="Enter Title"
                            onBlur={() => {
                                setCloseOnSave(false)
                                handleSave()
                            }}
                        />
                    </Label>
                    {formError?.element === 'title' && (
                        <p className="text-sm text-destructive mt-1">
                            {formError?.message}
                        </p>
                    )}
                </div>
                <div>
                    <Label label="Number of Guests" htmlFor="totalGuest">
                        <Input
                            id="totalGuest"
                            type="number"
                            min={1}
                            value={supernumerary?.totalGuest}
                            onChange={(e) => {
                                setSupernumerary({
                                    ...supernumerary,
                                    totalGuest: e.target.value,
                                })
                            }}
                            onBlur={() => {
                                setCloseOnSave(false)
                                handleSave()
                            }}
                        />
                    </Label>
                    {formError?.element === 'totalGuest' && (
                        <p className="text-sm text-destructive mt-1">
                            {formError?.message}
                        </p>
                    )}
                </div>
            </div>
            {!supernumeraryConfig ||
            supernumeraryConfig.find(
                (c: any) =>
                    c.title === 'Supernumerary_BriefingTime' &&
                    c.status != 'Off',
            ) ? (
                <Label label="Time of Briefing" htmlFor="time">
                    <TimeField
                        time={supernumerary?.briefingTime}
                        handleTimeChange={handleBriefingTimeChange}
                        timeID="time"
                        fieldName="Time"
                    />
                </Label>
            ) : null}
            {!supernumeraryConfig ||
            supernumeraryConfig.find(
                (c: any) =>
                    c.title === 'Supernumerary_GuestBriefing' &&
                    c.status != 'Off',
            ) ? (
                <CheckField>
                    <CheckFieldTopContent />
                    <CheckFieldContent>
                        <DailyCheckField
                            displayLabel="Guest Briefing Completed"
                            inputId="isBriefed"
                            handleNoChange={() => {
                                setSupernumerary({
                                    ...supernumerary,
                                    isBriefed: false,
                                })
                            }}
                            handleYesChange={() => {
                                setSupernumerary({
                                    ...supernumerary,
                                    isBriefed: true,
                                })
                            }}
                            hideCommentButton
                            defaultYesChecked={
                                supernumerary?.isBriefed === true
                            }
                            defaultNoChecked={
                                supernumerary?.isBriefed === false
                            }
                            disabled={locked}
                        />
                    </CheckFieldContent>
                </CheckField>
            ) : null}
            {supernumerary?.guestList?.nodes.length > 0 && (
                <Table className="border border-outer-space-400 border-dashed rounded-lg pt-2">
                    <TableHeader>
                        <TableRow className="hover:bg-transparent" noHoverEffect>
                            <TableHead className="text-left">Guests</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {supernumerary.guestList.nodes.map(
                            (guest: any, index: number) => (
                                <TableRow
                                    key={index}
                                    onClick={() => handleFirstNameClick(guest)}
                                    className="cursor-pointer">
                                    <TableCell className="text-left pl-2.5">
                                        {`${guest?.firstName || ''} ${guest?.surname || ''}`}
                                    </TableCell>
                                </TableRow>
                            ),
                        )}
                    </TableBody>
                </Table>
            )}
            <div className="flex justify-end gap-2">
                <Button
                    variant="secondary"
                    onClick={() => {
                        closeModal()
                    }}>
                    Cancel
                </Button>
                <Button
                    iconLeft={Check}
                    onClick={
                        locked
                            ? () => {}
                            : () => {
                                  setCloseOnSave(true)
                                  handleSave()
                              }
                    }
                    disabled={
                        loadingSupernumerary ||
                        createSupernumeraryLoading ||
                        updateSupernumeraryLoading
                    }>
                    {!currentEvent || +currentEvent.id === 0
                        ? 'Save'
                        : 'Update'}
                </Button>
            </div>
            <AlertDialogNew
                openDialog={openAddGuestDialog}
                setOpenDialog={setOpenAddGuestDialog}
                handleCreate={handleSaveGuest}
                handleDestructiveAction={handleDeleteGuest}
                destructiveActionText="Delete Guest"
                showDestructiveAction={currentGuest && currentGuest?.id > 0}
                title={
                    currentGuest && currentGuest?.id > 0
                        ? 'Update Guest'
                        : 'Add Guest'
                }
                actionText={
                    currentGuest && currentGuest?.id > 0
                        ? 'Update Guest'
                        : 'Save Guest'
                }
                size="xl"
                position="center">
                <div className="space-y-8">
                    {!locked && (
                        <>
                            <div className="flex gap-2">
                                {!supernumeraryConfig ||
                                supernumeraryConfig.find(
                                    (c: any) =>
                                        c.title === 'Supernumerary_FirstName' &&
                                        c.status != 'Off',
                                ) ? (
                                    <Label
                                        label="First Name"
                                        htmlFor="firstname">
                                        <Input
                                            id="firstname"
                                            type="text"
                                            placeholder="First Name"
                                            name="firstname"
                                            value={
                                                currentGuest
                                                    ? currentGuest?.firstName
                                                    : ''
                                            }
                                            onChange={(e) => {
                                                setCurrentGuest({
                                                    ...currentGuest,
                                                    firstName: e.target.value,
                                                })
                                            }}
                                        />
                                    </Label>
                                ) : null}
                                {!supernumeraryConfig ||
                                supernumeraryConfig.find(
                                    (c: any) =>
                                        c.title === 'Supernumerary_Surname' &&
                                        c.status != 'Off',
                                ) ? (
                                    <Label label="Surname" htmlFor="surname">
                                        <Input
                                            id="surname"
                                            type="text"
                                            placeholder="Surname"
                                            name="surname"
                                            value={
                                                currentGuest
                                                    ? currentGuest?.surname
                                                    : ''
                                            }
                                            onChange={(e) => {
                                                setCurrentGuest({
                                                    ...currentGuest,
                                                    surname: e.target.value,
                                                })
                                            }}
                                        />
                                    </Label>
                                ) : null}
                            </div>
                            <div>
                                {!supernumeraryConfig ||
                                supernumeraryConfig.find(
                                    (c: any) =>
                                        c.title === 'Supernumerary_Signature' &&
                                        c.status != 'Off',
                                ) ? (
                                    <SignaturePadComponent
                                        signature={
                                            currentGuest?.sectionSignature
                                        }
                                        onSignatureChanged={onSignatureChanged}
                                    />
                                ) : null}
                            </div>
                            {!supernumeraryConfig ||
                            supernumeraryConfig.find(
                                (c: any) =>
                                    c.title === 'Supernumerary_Policies' &&
                                    c.status != 'Off',
                            ) ? (
                                <div className="md:col-span-2 mt-4">
                                    <P className="mb-4">
                                        Please read and accept the following
                                        policies
                                    </P>
                                    {logBookConfig && (
                                        <Table className="border border-outer-space-400 border-dashed rounded-lg">
                                            <TableHeader>
                                                <TableRow className="hover:bg-transparent" noHoverEffect>
                                                    <TableHead className="w-3/4 text-left pt-1">
                                                        Policy
                                                    </TableHead>
                                                    <TableHead className="w-1/4 text-left pt-1">
                                                        Action
                                                    </TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {logBookConfig?.policies?.nodes.map(
                                                    (
                                                        policy: any,
                                                        index: number,
                                                    ) => (
                                                        <TableRow key={index}>
                                                            <TableCell className="text-left">
                                                                {policy.title}
                                                            </TableCell>
                                                            <TableCell>
                                                                <a
                                                                    href={
                                                                        process
                                                                            .env
                                                                            .FILE_BASE_URL +
                                                                        policy.fileFilename
                                                                    }
                                                                    className="font-medium hover:underline"
                                                                    target="_blank">
                                                                    View
                                                                </a>
                                                            </TableCell>
                                                        </TableRow>
                                                    ),
                                                )}
                                            </TableBody>
                                        </Table>
                                    )}
                                </div>
                            ) : null}
                        </>
                    )}
                </div>
            </AlertDialogNew>
        </div>
    )
}

// Using the imported SignaturePadComponent
