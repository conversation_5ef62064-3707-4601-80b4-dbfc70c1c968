'use client'

import {
    CreateDangerousGoodsRecord,
    UpdateDangerousGoodsRecord,
    UpdateTripReport_Stop,
} from '@/app/lib/graphQL/mutation'
import { useMutation } from '@apollo/client'
import React, { useEffect, useState } from 'react'
import Select from 'react-select'
import Editor from '../editor'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import PVPDRiskAnalysis from './forms/pvpd-risk-analysis'
import DangerousGoodsRecordModel from '@/app/offline/models/dangerousGoodsRecord'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import TripReport_StopModel from '@/app/offline/models/tripReport_Stop'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { CheckFieldLabel } from '@/components/ui/check-field-label'
import {
    Sheet,
    She<PERSON><PERSON>ontent,
    She<PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Sheet<PERSON><PERSON>,
} from '@/components/ui/sheet'
import { Trash2, Plus } from 'lucide-react'
import { toast } from 'sonner'
import {
    Separator,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '@/components/ui'

export default function PVPDDGR({
    locked,
    currentTrip,
    logBookConfig,
    selectedDGR,
    setSelectedDGR,
    members,
    displayDangerousGoods,
    displayDangerousGoodsSailing,
    setDisplayDangerousGoods,
    setDisplayDangerousGoodsSailing,
    allDangerousGoods,
    setAllDangerousGoods,
    currentEvent,
    offline = false,
}: {
    locked: boolean
    currentTrip: any
    logBookConfig: any
    selectedDGR: any
    setSelectedDGR: any
    members: any
    displayDangerousGoods: any
    displayDangerousGoodsSailing: any
    setDisplayDangerousGoods: any
    setDisplayDangerousGoodsSailing: any
    allDangerousGoods: any
    setAllDangerousGoods: any
    currentEvent: any
    offline?: boolean
}) {
    const [selectedRow, setSelectedRow] = useState<any>(false)
    const [currentDangerousGoods, setCurrentDangerousGoods] = useState<any>([])
    const [openRiskAnalysis, setOpenTripStartRiskAnalysis] = useState(false)
    const [permissions, setPermissions] = useState<any>(false)
    const [editDGR, setEditDGR] = useState<any>(false)
    const [allChecked, setAllChecked] = useState<any>(false)
    const dangerousGoodsRecordModel = new DangerousGoodsRecordModel()
    const tripReport_StopModel = new TripReport_StopModel()

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_LOGBOOKENTRY_RISK_ANALYSIS', permissions)) {
                setEditDGR(true)
            } else {
                setEditDGR(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    useEffect(() => {
        if (selectedDGR > 0) {
            setSelectedRow(selectedDGR)
        }
    }, [selectedDGR])

    useEffect(() => {
        if (
            currentEvent?.dangerousGoodsRecords?.nodes?.length > 0 &&
            !allDangerousGoods
        ) {
            setAllDangerousGoods(currentEvent?.dangerousGoodsRecords?.nodes)
        }
    }, [currentEvent])

    const displayField = (fieldName: string) => {
        const dailyChecks =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'EventType_LogBookComponent',
            )
        if (
            dailyChecks?.length > 0 &&
            dailyChecks[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const displayReportField = (fieldName: string) => {
        const dailyChecks =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'TripReport_LogBookComponent',
            )
        if (
            dailyChecks?.length > 0 &&
            dailyChecks[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const goodsTypes = [
        { label: 'Class 1 Explosives', value: '1' },
        { label: 'Class 2 Gases', value: '2' },
        { label: 'Class 2.1 - Flammable gases', value: '2.1' },
        { label: 'Class 2.2 - Non-Flammable Non-Toxic Gases', value: '2.2' },
        { label: 'Class 2.3 - Toxic Gases', value: '2.3' },
        { label: 'Class 3 Flammable Liquids', value: '3' },
        { label: 'Class 4 Flammable Solids', value: '4' },
        { label: 'Class 4.1 - Flammable Solids', value: '4.1' },
        {
            label: 'Class 4.2 - Spontaneously Combustible Substances',
            value: '4.2',
        },
        { label: 'Class 4.3 - Substances Flammable When Wet', value: '4.3' },
        {
            label: 'Class 5 Oxidizing Substances and Organic Peroxides',
            value: '5',
        },
        { label: 'Class 5.1 - Oxidising Substances', value: '5.1' },
        { label: 'Class 5.2 - Organic Peroxides', value: '5.2' },
        { label: 'Class 6 Toxic and Infectious Substances', value: '6' },
        { label: 'Class 6.1 - Toxic Substances', value: '6.1' },
        { label: 'Class 6.2 - Infectious Substances', value: '6.2' },
        { label: 'Class 7 Radioactive Substances', value: '7' },
        { label: 'Class 8 Corrosive Substances', value: '8' },
        { label: 'Class 9 Miscellaneous Hazardous Substance', value: '9' },
    ]

    const handleAddNewDangerousGoods = async () => {
        if (currentEvent.id > 0) {
            setCurrentDangerousGoods(false)
            setSelectedRow(false)
            const dgr = {
                type: currentDangerousGoods?.type,
                comment: currentDangerousGoods?.comment,
                tripReport_StopID: +currentEvent.id,
            }
            if (offline) {
                // createDangerousGoodsRecord
                const data = await dangerousGoodsRecordModel.save({
                    ...dgr,
                    id: generateUniqueId(),
                })
                if (allDangerousGoods) {
                    setAllDangerousGoods([...allDangerousGoods, data])
                } else {
                    setAllDangerousGoods([data])
                }
                setSelectedRow(false)
            } else {
                createDangerousGoodsRecord({
                    variables: {
                        input: {
                            ...dgr,
                        },
                    },
                })
            }
        } else {
            toast.error('Please save the trip before adding dangerous goods')
        }
    }

    const [createDangerousGoodsRecord] = useMutation(
        CreateDangerousGoodsRecord,
        {
            onCompleted: (response) => {
                const data = response.createDangerousGoodsRecord
                if (allDangerousGoods) {
                    setAllDangerousGoods([...allDangerousGoods, data])
                } else {
                    setAllDangerousGoods([data])
                }
                setSelectedRow(false)
            },
            onError: (error) => {
                console.error('Error creating dangerous goods record', error)
            },
        },
    )

    const [updateDangerousGoodsRecord] = useMutation(
        UpdateDangerousGoodsRecord,
        {
            onCompleted: (response) => {},
            onError: (error) => {
                console.error('Error updating dangerous goods record', error)
            },
        },
    )

    const handleSetDisplayDangerousGoodsSailing = async (value: boolean) => {
        setDisplayDangerousGoodsSailing(value)
        if (offline) {
            await tripReport_StopModel.save({
                id: +currentEvent.id,
                designatedDangerousGoodsSailing: value ? value : false,
            })
        } else {
            updateTripReport_Stop({
                variables: {
                    input: {
                        id: +currentEvent.id,
                        designatedDangerousGoodsSailing: value ? value : false,
                    },
                },
            })
        }
    }

    const [updateTripReport_Stop] = useMutation(UpdateTripReport_Stop, {
        onCompleted: (response) => {},
        onError: (error) => {
            console.error('Error updating trip report stop', error)
        },
    })

    const deleteDangerousGoods = async () => {
        if (offline) {
            await dangerousGoodsRecordModel.save({
                id: +selectedRow,
                tripReport_StopID: 0,
            })
        } else {
            updateDangerousGoodsRecord({
                variables: {
                    input: {
                        id: +selectedRow,
                        tripReport_StopID: 0,
                    },
                },
            })
        }
        setAllDangerousGoods(
            allDangerousGoods.filter((dgr: any) => dgr.id !== selectedRow),
        )
    }

    return (
        <div key={`${currentTrip.id}`}>
            {displayField('DangerousGoods') && (
                <>
                    <div
                        className={`flex justify-between flex-col sm:flex-row gap-4 w-full`}>
                        <div className={`flex flex-wrap items-center gap-4`}>
                            <Tooltip>
                                <TooltipTrigger className='text-start'>
                                    <CheckFieldLabel
                                        type="checkbox"
                                        id="pvpd-log-dgr"
                                        checked={displayDangerousGoods}
                                        onCheckedChange={(checked) => {
                                            setDisplayDangerousGoods(checked)
                                        }}
                                        disabled={locked || !currentEvent}
                                        label="Dangerous goods carried"
                                    />
                                </TooltipTrigger>
                                <TooltipContent hidden={currentEvent?.id > 0}>
                                    Please save the trip before adding dangerous
                                    goods
                                </TooltipContent>
                            </Tooltip>

                            {displayField(
                                'DesignatedDangerousGoodsSailing',
                            ) && (
                                <CheckFieldLabel
                                    type="checkbox"
                                    id="pvpd-log-dgs"
                                    checked={displayDangerousGoodsSailing}
                                    onCheckedChange={(checked) => {
                                        handleSetDisplayDangerousGoodsSailing(
                                            checked,
                                        )
                                    }}
                                    disabled={locked}
                                    label="This is a designated dangerous goods sailing"
                                />
                            )}
                        </div>
                        <Tooltip>
                            <TooltipTrigger>
                                <CheckFieldLabel
                                    type="checkbox"
                                    id="dangerous-goods-risk-analysis"
                                    checked={
                                        allChecked ||
                                        currentEvent?.dangerousGoodsChecklist
                                            ?.id > 0
                                    }
                                    onCheckedChange={(checked) => {
                                        setOpenTripStartRiskAnalysis(true)
                                    }}
                                    disabled={locked || !currentEvent}
                                    label="Dangerous goods - risk analysis"
                                    variant="warning"
                                    className="h-fit text-start"
                                />
                            </TooltipTrigger>
                            <TooltipContent hidden={currentEvent?.id > 0}>
                                Please save the trip before adding dangerous
                                goods
                            </TooltipContent>
                        </Tooltip>
                    </div>
                    {
                        <>
                            {allDangerousGoods &&
                                allDangerousGoods?.map((dgr: any) => (
                                    <div key={`${dgr.id}`}>
                                        <div
                                            key={`${dgr.id}-header`}
                                            className={`group border-b text-left  hover:bg-white /90`}
                                            onClick={() => {
                                                if (selectedRow === dgr.id) {
                                                    setSelectedRow(false)
                                                    setSelectedDGR(false)
                                                } else {
                                                    setSelectedRow(dgr.id)
                                                    setSelectedDGR(dgr.id)
                                                    setCurrentDangerousGoods(
                                                        dgr,
                                                    )
                                                }
                                            }}>
                                            <div>
                                                <div className="p-4">
                                                    {goodsTypes &&
                                                        (goodsTypes.find(
                                                            (option) =>
                                                                option.value ===
                                                                dgr?.type,
                                                        )?.label ??
                                                            ' --- Add goods --- ')}
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            key={`${dgr.id}-body`}
                                            className={`${selectedRow === dgr.id ? 'bg-slblue-100 ' : 'hidden'} text-left`}>
                                            <div>
                                                <div className="p-4 rounded-lg">
                                                    <div className="my-4">
                                                        <Select
                                                            id="dangerous-goods-type"
                                                            options={goodsTypes}
                                                            onChange={async (
                                                                selectedOption: any,
                                                            ) => {
                                                                setCurrentDangerousGoods(
                                                                    {
                                                                        ...currentDangerousGoods,
                                                                        type: selectedOption.value,
                                                                    },
                                                                )
                                                                if (offline) {
                                                                    await dangerousGoodsRecordModel.save(
                                                                        {
                                                                            id: +dgr.id,
                                                                            type: selectedOption.value,
                                                                        },
                                                                    )
                                                                } else {
                                                                    updateDangerousGoodsRecord(
                                                                        {
                                                                            variables:
                                                                                {
                                                                                    input: {
                                                                                        id: +dgr.id,
                                                                                        type: selectedOption.value,
                                                                                    },
                                                                                },
                                                                        },
                                                                    )
                                                                }
                                                                setAllDangerousGoods(
                                                                    allDangerousGoods.map(
                                                                        (
                                                                            dgr: any,
                                                                        ) =>
                                                                            dgr.id ===
                                                                            selectedRow
                                                                                ? {
                                                                                      ...dgr,
                                                                                      type: selectedOption.value,
                                                                                  }
                                                                                : dgr,
                                                                    ),
                                                                )
                                                            }}
                                                            value={goodsTypes.find(
                                                                (option) =>
                                                                    option.value ===
                                                                    (currentDangerousGoods?.type
                                                                        ? currentDangerousGoods?.type
                                                                        : dgr?.type),
                                                            )}
                                                            menuPlacement="top"
                                                            placeholder="Dangerous goods type"
                                                            className="w-full bg-gray-100 rounded text-sm"
                                                            classNames={{
                                                                control: () =>
                                                                    'flex py-1 w-full !text-sm !text-gray-900 !bg-transparent !rounded-lg !border !border-gray-200 focus:ring-blue-500 focus:border-blue-500   ',
                                                                singleValue:
                                                                    () => '',
                                                                dropdownIndicator:
                                                                    () =>
                                                                        '!p-0 !hidden',
                                                                menu: () => '',
                                                                indicatorSeparator:
                                                                    () =>
                                                                        '!hidden',
                                                                multiValue:
                                                                    () =>
                                                                        '!bg-sky-100 inline-flex rounded p-1 m-0 !mr-1.5 border border-sky-300 !rounded-md !text-sky-900 font-normal mr-2',
                                                                clearIndicator:
                                                                    () =>
                                                                        '!py-0',
                                                                valueContainer:
                                                                    () =>
                                                                        '!py-0',
                                                            }}
                                                        />
                                                    </div>
                                                    <div className="my-4">
                                                        <Editor
                                                            id={`dangerous-goods-description`}
                                                            placeholder="Dangerous goods description"
                                                            className={`w-full`}
                                                            content={
                                                                currentDangerousGoods?.comment
                                                                    ? currentDangerousGoods?.comment
                                                                    : dgr?.comment
                                                            }
                                                            handleEditorChange={(
                                                                content: string,
                                                            ) => {
                                                                setCurrentDangerousGoods(
                                                                    {
                                                                        ...currentDangerousGoods,
                                                                        comment:
                                                                            content,
                                                                    },
                                                                )
                                                            }}
                                                            handleEditorBlur={async () => {
                                                                if (offline) {
                                                                    await dangerousGoodsRecordModel.save(
                                                                        {
                                                                            id: +dgr.id,
                                                                            comment:
                                                                                currentDangerousGoods?.comment,
                                                                        },
                                                                    )
                                                                } else {
                                                                    updateDangerousGoodsRecord(
                                                                        {
                                                                            variables:
                                                                                {
                                                                                    input: {
                                                                                        id: +dgr.id,
                                                                                        comment:
                                                                                            currentDangerousGoods?.comment,
                                                                                    },
                                                                                },
                                                                        },
                                                                    )
                                                                }
                                                                setAllDangerousGoods(
                                                                    allDangerousGoods.map(
                                                                        (
                                                                            dgr: any,
                                                                        ) =>
                                                                            dgr.id ===
                                                                            selectedRow
                                                                                ? {
                                                                                      ...dgr,
                                                                                      comment:
                                                                                          currentDangerousGoods?.comment,
                                                                                  }
                                                                                : dgr,
                                                                    ),
                                                                )
                                                            }}
                                                        />
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <Button
                                                            variant="text"
                                                            iconLeft={Trash2}
                                                            onClick={() => {
                                                                deleteDangerousGoods()
                                                            }}>
                                                            Delete
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            <Separator className="my-4" />
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="outline"
                                        iconLeft={Plus}
                                        onClick={handleAddNewDangerousGoods}
                                        disabled={locked || !currentEvent}>
                                        Add Dangerous Goods
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent hidden={currentEvent?.id > 0}>
                                    Please save the trip before adding dangerous
                                    goods
                                </TooltipContent>
                            </Tooltip>
                        </>
                    }
                </>
            )}

            {currentEvent && (
                <PVPDRiskAnalysis
                    offline={offline}
                    onSidebarClose={() => setOpenTripStartRiskAnalysis(false)}
                    currentTrip={currentTrip}
                    crewMembers={members}
                    open={openRiskAnalysis}
                    onOpenChange={setOpenTripStartRiskAnalysis}
                    editDGR={editDGR}
                    currentEvent={currentEvent}
                    setAllChecked={setAllChecked}
                />
            )}
        </div>
    )
}
