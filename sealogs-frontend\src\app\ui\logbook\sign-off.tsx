'use client'
import React, { useEffect, useState } from 'react'
import { useMutation, useLazyQuery } from '@apollo/client'
import {
    CREATE_ENGINESTARTSTOP,
    CREATE_FUELLOG,
    CREATE_R2FILE,
    CreateLogBookSignOff_LogBookEntrySection,
    CreateRefuellingBunkering,
    CreateTripEvent,
    CreateTripReport_LogBookEntrySection,
    UPDATE_ENGINE,
    UPDATE_FUELLOG,
    UpdateCrewMembers_LogBookEntrySection,
    UpdateFuelTank,
    UpdateLogBookSignOff_LogBookEntrySection,
} from '@/app/lib/graphQL/mutation'
import {
    GET_ENGINES,
    GET_FUELTANKS,
    GET_GEO_LOCATIONS,
    GET_LOGBOOK_ENTRY_BY_ID,
    GET_SECTION_MEMBER_COMMENTS,
    GET_SECTION_MEMBER_IMAGES,
    GET_VESSEL_POSITION,
    TripReport_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
import {
    UPDATE_SECTION_MEMBER_COMMENT,
    CREATE_SECTION_MEMBER_COMMENT,
    UPDATE_LOGBOOK_ENTRY,
    CreateLogBookEntrySection_Signature,
    CREATE_VESSEL_POSITION,
} from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'
import { useSearchParams } from 'next/navigation'
import { SLALL_LogBookFields } from '@/app/lib/logbook-configuration'
import vesselTypes from '@/app/lib/vesselTypes'

import { getPermissions, hasPermission, isCrew } from '@/app/helpers/userHelper'
import LocationField from './components/location/location'
import { isEmpty } from 'lodash'
import { GetCrewListWithTrainingStatus } from '@/app/lib/actions'
import dayjs from 'dayjs'
import TimeField from './components/time'
import OpenPreviousLogbookComments from './open-previous-comments'
import { displayDescriptionIcon } from '../daily-checks/actions'
// import { XMarkIcon } from '@heroicons/react/24/outline'
import 'react-quill/dist/quill.snow.css'
import { SealogsFuelIcon } from '@/app/lib/icons/SealogsFuelIcon'
import EngineModel from '@/app/offline/models/engine'
import FuelTankModel from '@/app/offline/models/fuelTank'
import GeoLocationModel from '@/app/offline/models/geoLocation'
import VehiclePositionModel from '@/app/offline/models/vehiclePosition'
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import LogBookSignOff_LogBookEntrySectionModel from '@/app/offline/models/logBookSignOff_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import FuelLogModel from '@/app/offline/models/fuelLog'
import CrewMembers_LogBookEntrySectionModel from '@/app/offline/models/crewMembers_LogBookEntrySection'
import LogBookEntryModel from '@/app/offline/models/logBookEntry'
import LogBookEntrySection_SignatureModel from '@/app/offline/models/logBookEntrySection_Signature'
import { getSignoffFields } from '@/app/lib/dailyCheckFields'
import { FooterWrapper } from '@/components/footer-wrapper'

// Shadcn UI components
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { toast } from 'sonner'
import { H4, P } from '@/components/ui/typography'
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'

// Lucide icons
import { X, Check, ArrowLeft, AlertCircle } from 'lucide-react'

import SignaturePad from '@/components/signature-pad'
import {
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import UploadCloudFlare from './components/upload-cf'
import { Button, Card, Separator } from '@/components/ui'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'

export default function LogEntrySignOff({
    logBookConfig,
    signOff = false,
    updateSignOff = false,
    updateTripReport,
    fuel = false,
    locked = false,
    crewMembers = false,
    vessel = false,
    logBook = false,
    masterTerm = 'Master',
    prevComments = false,
    screen = 'Desktop',
    onUpdatePrevComments,
    offline = false,
}: {
    logBookConfig: any
    signOff: any
    updateSignOff: any
    updateTripReport?: any
    fuel: any
    locked: any
    crewMembers: any
    vessel: any
    logBook: any
    masterTerm: any
    prevComments: any
    screen: any
    onUpdatePrevComments: any
    offline?: boolean
}) {
    const router = useRouter()
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = searchParams.get('logentryID') ?? 0
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [currentComment, setCurrentComment] = useState<any>('')
    const [currentField, setCurrentField] = useState<any>('')
    const [comments, setComments] = useState<any>(false)
    const [signature, setSignature] = useState<any>(false)
    const [engineList, setEngineList] = useState<any>(null)
    const [selectedLocation, setSelectedLocation] = useState<any>(null)
    const [reloadTimer, setReloadTimer] = useState<any>(false)

    const [currentLocation, setCurrentLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })
    const [geoLocations, setLocations] = useState<any>(false)
    const [geoLocation, setGeoLocation] = useState<any>(false)
    const [imCrew, setImCrew] = useState<any>(false)
    const [crew, setCrew] = useState<any>(false)
    const [fuelStart, setFuelStart] = useState<any>(false)
    const [sectionComment, setSectionComment] = useState<any>('')
    const [time, setTime] = useState<any>(dayjs().format('HH:mm'))
    const [dismissPrevComment, setDismissPrevComment] = useState<any>(false)
    const [isLocked, setIsLocked] = useState<any>(locked)
    const [prevComment, setPrevComment] = useState<any>(false)
    const [unrelevantComments, setUnrelevantComments] = useState([] as any)
    const [filteredFields, setFilteredFields] = useState<any>(false)
    const [openDescriptionPanel, setOpenDescriptionPanel] = useState(false)
    const [descriptionPanelContent, setDescriptionPanelContent] = useState('')
    const [descriptionPanelHeading, setDescriptionPanelHeading] = useState('')
    const [fuelTankList, setFuelTankList] = useState<any>(false)
    const [openRefuelling, setOpenRefuelling] = useState(false)
    const [refuelling, setRefuelling] = useState<any>({
        fuelAdded: 0,
        costPerLitre: 0,
        totalCost: 0,
    })
    const [currentRefuellingLocation, setCurrentRefuellingLocation] =
        useState<any>(null)
    const [currentFuelTank, setCurrentFuelTank] = useState<any>(null)
    const [tripReport, setTripReport] = useState<any>([])
    const [fuelReceipts, setFuelReceipts] = useState<any>([])
    const [fieldImages, setFieldImages] = useState<any>(false)

    const engineModel = new EngineModel()
    const fuelTankModel = new FuelTankModel()
    const fuelLogModel = new FuelLogModel()
    const geoLocationModel = new GeoLocationModel()
    const vehiclePositionModel = new VehiclePositionModel()
    const commentModel = new SectionMemberCommentModel()
    const signOffModel = new LogBookSignOff_LogBookEntrySectionModel()
    const lbCrewModel = new CrewMembers_LogBookEntrySectionModel()
    const logBookModel = new LogBookEntryModel()
    const signatureModel = new LogBookEntrySection_SignatureModel()
    const [permissions, setPermissions] = useState<any>(false)
    const [closeLogBookEntry, setCloseLogBookEntry] = useState<any>(false)

    // Breakpoints for responsive design
    const bp = useBreakpoints()

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('CLOSE_LOGBOOKENTRY', permissions)) {
                setCloseLogBookEntry(true)
            } else {
                setCloseLogBookEntry(false)
            }
        }
    }

    const [getFieldImages] = useLazyQuery(GET_SECTION_MEMBER_IMAGES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readCaptureImages.nodes
            if (data) {
                setFieldImages(data)
            }
        },
        onError: (error: any) => {
            console.error('getFieldImages error', error)
        },
    })

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
        getFieldImages({
            variables: {
                filter: {
                    logBookEntryID: { eq: logentryID },
                },
            },
        })
    }, [])

    const refreshImages = async () => {
        await getFieldImages({
            variables: {
                filter: {
                    logBookEntryID: { eq: logentryID },
                },
            },
        })
    }

    useEffect(() => {
        if (signOff?.endLocation?.geoLocationID > 0) {
            setSelectedLocation({
                label: signOff.endLocation.geoLocation.title,
                value: signOff.endLocation.geoLocation.id,
                latitude: signOff.endLocation.geoLocation.lat,
                longitude: signOff.endLocation.geoLocation.long,
            })
        }
    }, [signOff])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    useEffect(() => {
        if (crewMembers) {
            const data = crewMembers.map((crew: any) => {
                return {
                    ...crew,
                    crewMember: GetCrewListWithTrainingStatus(
                        [crew.crewMember],
                        [vessel],
                    )[0],
                }
            })
            setCrew(data)
        }
    }, [crewMembers])

    const [queryGetFuelTanks] = useLazyQuery(GET_FUELTANKS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readFuelTanks.nodes
            setFuelTankList(data)
        },
        onError: (error: any) => {
            console.error('getFuelTanks error', error)
        },
    })

    const getFuelTanks = async (fuelTankIds: any) => {
        if (offline) {
            const data = await fuelTankModel.getByIds(fuelTankIds)
            setFuelTankList(data)
        } else {
            await queryGetFuelTanks({
                variables: {
                    id: fuelTankIds,
                },
            })
        }
    }

    useEffect(() => {
        if (vessel.vesselType) {
            const logbookFields = SLALL_LogBookFields.filter((field: any) => {
                if (field?.items?.length > 0) {
                    return field.vesselType.includes(
                        vesselTypes.indexOf(vessel?.vesselType),
                    )
                }
                return false
            })
            var filteredFields: any = []
            logbookFields.map((logbookField: any) => {
                var currentField = logbookField
                var currentFieldItems: any = []
                logbookField.items.map((fields: any) => {
                    if (
                        fields.vesselType.includes(
                            vesselTypes.indexOf(vessel?.vesselType),
                        )
                    ) {
                        currentFieldItems.push(fields)
                    }
                })
                currentField.items = currentFieldItems
                filteredFields.push(currentField)
            })
            setFilteredFields(filteredFields)
            const fuelTankIds = vessel?.parentComponent_Components?.nodes
                .filter(
                    (item: any) =>
                        item.basicComponent.componentCategory === 'FuelTank',
                )
                .map((item: any) => {
                    return item.basicComponent.id
                })
            fuelTankIds.length > 0 && getFuelTanks(fuelTankIds)
        }
    }, [vessel])

    const getSlallFields = () => {
        return filteredFields ? filteredFields : SLALL_LogBookFields
    }

    const [loadLocations] = useLazyQuery(GET_GEO_LOCATIONS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const locations = response.readGeoLocations.nodes?.map(
                (location: any) => ({
                    label: location.title,
                    value: location.id,
                    latitude: location.lat,
                    longitude: location.long,
                }),
            )
            setGeoLocation(response.readGeoLocations.nodes)
            setLocations(locations)
            getlastLocation()
        },
        onError: (error) => {
            console.error('Error loading locations', error)
        },
    })

    useEffect(() => {
        setFuelStart(signOff?.fuelStart)
        if (fuel && fuel[0]?.fuelTankStartStops?.nodes?.length > 0) {
            setFuelStart(
                signOff?.fuelStart
                    ? signOff.fuelStart
                    : fuel[0]?.fuelTankStartStops?.nodes[0]?.start,
            )
        }
        if (signOff?.completedTime) {
            setTime(
                signOff.completedTime.split(':')[0] +
                    ':' +
                    signOff.completedTime.split(':')[1],
            )
        }
        if (signOff?.endLocationID) {
            if (signOff.endLocation.geoLocationID > 0 && geoLocations) {
                const location = geoLocations?.find(
                    (location: any) =>
                        location.id === signOff.endLocation.geoLocationID,
                )
                if (location) {
                    setSelectedLocation({
                        label: location.title,
                        value: location.id,
                        latitude: location.lat,
                        longitude: location.long,
                    })
                }
            } else {
                setCurrentLocation({
                    latitude: signOff.endLocation.lat,
                    longitude: signOff.endLocation.long,
                })
            }
        }
    }, [fuel, signOff])

    const getlastLocation = async () => {
        if (signOff?.endLocationID === 0) {
            if (offline) {
                const vpData =
                    await vehiclePositionModel.getByVehicleId(vesselID)
                const sortedData = vpData.sort((a: any, b: any) => {
                    return b.id.localeCompare(a.id, undefined, {
                        numeric: true,
                    })
                })
                if (sortedData.length > 0) {
                    const data = sortedData[0]
                    if (+data.geoLocation?.id > 0) {
                        setSelectedLocation({
                            label: data.geoLocation.title,
                            value: data.geoLocation.id,
                            latitude: data.geoLocation.lat,
                            longitude: data.geoLocation.long,
                        })
                    }
                    setCurrentLocation({
                        latitude: data.lat,
                        longitude: data.long,
                    })
                }
            } else {
                await queryVesselPosition({
                    variables: {
                        id: +vesselID,
                    },
                })
            }
        }
    }

    const [queryVesselPosition] = useLazyQuery(GET_VESSEL_POSITION, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneVehiclePosition
            if (data.geoLocation?.id > 0) {
                setSelectedLocation({
                    label: data.geoLocation.title,
                    value: data.geoLocation.id,
                    latitude: data.geoLocation.lat,
                    longitude: data.geoLocation.long,
                })
            }
            setCurrentLocation({
                latitude: data.lat,
                longitude: data.long,
            })
        },
        onError: (error: any) => {
            console.error('queryVesselPosition error', error)
        },
    })
    const loadGeoLocations = async () => {
        if (offline) {
            const data = await geoLocationModel.getAll()
            const locations = data?.map((location: any) => ({
                label: location.title,
                value: location.id,
                latitude: location.lat,
                longitude: location.long,
            }))
            setGeoLocation(data)
            setLocations(locations)
            getlastLocation()
        } else {
            loadLocations()
        }
    }
    useEffect(() => {
        setImCrew(isCrew())
        if (!geoLocations) {
            loadGeoLocations()
        }
        if (logBook && logBook.status === 'Locked') {
            setIsLocked(true)
        }
    }, [])

    const handleHullChecks = async (check: Boolean, value: any) => {
        if (+signOff?.id > 0) {
            const variables = {
                id: signOff.id,
                [value]: check ? 'Ok' : 'Not_Ok',
            }
            if (offline) {
                const data = await signOffModel.save(variables)
                updateSignOff(data)
                if (reloadTimer > 0) {
                    setReloadTimer(reloadTimer + 1)
                }
            } else {
                updateLogBookSignOff_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const getComment = (fieldName: string, commentType = 'FieldComment') => {
        if (!comments) return false
        // Sort comments in descending order
        const sortedComments = [...comments]?.sort(
            (a: any, b: any) => b.id - a.id,
        )
        const comment =
            sortedComments?.length > 0
                ? sortedComments.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : []
        return comment.length > 0 ? comment[0] : false
    }

    const composeField = (fieldName: string) => {
        var composedField: { fleldID: any; fieldName: any }[] = []
        const signOff =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'LogBookSignOff_LogBookComponent',
            )
        if (
            signOff?.length > 0 &&
            signOff[0]?.customisedComponentFields?.nodes.map((field: any) =>
                field.fieldName === fieldName
                    ? composedField.push({
                          fleldID: field.id,
                          fieldName: field.fieldName,
                      })
                    : '',
            )
        ) {
            return composedField
        }
        return false
    }

    const showCommentPopup = (comment: string, field: any) => {
        setCurrentComment(comment ? comment : '')
        setCurrentField(field)
        setOpenCommentAlert(true)
    }

    const handleSaveComment = async () => {
        setOpenCommentAlert(false)
        const comment = (document.getElementById('comment') as HTMLInputElement)
            ?.value
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: currentField[0]?.fieldName,
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: signOff.id,
            commentType: 'FieldComment',
        }
        const offlineID = currentComment?.id
            ? currentComment?.id
            : generateUniqueId()
        if (currentComment) {
            if (offline) {
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            setCurrentComment(comment)
            if (offline) {
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
                setCurrentComment('')
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const [
        updateSectionMemberComment,
        { loading: updateSectionMemberCommentLoading },
    ] = useMutation(UPDATE_SECTION_MEMBER_COMMENT, {
        onCompleted: (response) => {
            loadSectionMemberComments()
        },
        onError: (error) => {
            console.error('Error updating comment', error)
        },
    })

    const [
        createSectionMemberComment,
        { loading: createSectionMemberCommentLoading },
    ] = useMutation(CREATE_SECTION_MEMBER_COMMENT, {
        onCompleted: (response) => {
            loadSectionMemberComments()
            setCurrentComment('')
        },
        onError: (error) => {
            console.error('Error creating comment', error)
        },
    })

    const [querySectionMemberComments] = useLazyQuery(
        GET_SECTION_MEMBER_COMMENTS,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                    setSectionComment(
                        data.filter(
                            (comment: any) => comment.commentType === 'Section',
                        )[0]?.comment,
                    )
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (offline) {
            const data = await commentModel.getByLogBookEntrySectionID(
                signOff.id,
            )
            if (data) {
                setComments(data)
                setSectionComment(
                    data.filter(
                        (comment: any) => comment.commentType === 'Section',
                    )[0]?.comment,
                )
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: signOff.id },
                    },
                },
            })
        }
    }

    const createOfflineLogBookSignOff = async () => {
        const id = generateUniqueId()
        const data = await signOffModel.save({
            id: id,
            logBookEntryID: logentryID,
        })
        updateSignOff(data)
    }
    useEffect(() => {
        if (!signOff && screen === 'Desktop') {
            if (offline) {
                createOfflineLogBookSignOff()
            } else {
                createLogBookSignOff_LogBookEntrySection({
                    variables: {
                        input: {
                            logBookEntryID: logentryID,
                        },
                    },
                })
            }
        }
        if (signOff) {
            loadSectionMemberComments()
        }
    }, [])

    const displayField = (fieldName: string) => {
        const signOff =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'LogBookSignOff_LogBookComponent',
            )
        if (
            signOff?.length > 0 &&
            signOff[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const displayGroupField = (groupField: any) => {
        const display = groupField.items.filter((field: any) => {
            return displayField(field.name)
        })
        return display.length > 0 ? true : false
    }

    const getFieldLabel = (fieldName: string) => {
        const defaultConfig = SLALL_LogBookFields.map(
            (component: any) => component,
        )
        var title = fieldName
        defaultConfig.forEach((defaultLogBookComponents: any) => {
            defaultLogBookComponents.items.forEach((defaultField: any) => {
                if (fieldName === defaultField.value) {
                    title = defaultField?.label
                        ? defaultField?.label
                        : fieldName
                }
            })
        })

        const signOff =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'LogBookSignOff_LogBookComponent',
            )
        if (signOff?.length > 0) {
            const customField =
                signOff[0]?.customisedComponentFields?.nodes.find(
                    (field: any) => field.fieldName === fieldName,
                )
            if (customField?.customisedFieldTitle) {
                return customField?.customisedFieldTitle
            } else {
                return title
            }
        }
        return title
    }

    const displayDescription = (fieldName: string) => {
        const signOff =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'LogBookSignOff_LogBookComponent',
            )
        if (
            signOff?.length > 0 &&
            signOff[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            const description =
                signOff[0]?.customisedComponentFields?.nodes.filter(
                    (field: any) =>
                        field.fieldName === fieldName && field.status !== 'Off',
                )[0].description
            return description
        }
        return ''
    }

    const [
        updateLogBookSignOff_LogBookEntrySection,
        { loading: updateLogBookSignOff_LogBookEntrySectionLoading },
    ] = useMutation(UpdateLogBookSignOff_LogBookEntrySection, {
        onCompleted: (response) => {
            const data = response.updateLogBookSignOff_LogBookEntrySection
            updateSignOff(data)
            if (reloadTimer > 0) {
                setReloadTimer(reloadTimer + 1)
            }
        },
        onError: (error) => {
            console.error('Error completing safety check', error)
        },
    })

    const [
        createLogBookSignOff_LogBookEntrySection,
        { loading: createLogBookSignOff_LogBookEntrySectionLoading },
    ] = useMutation(CreateLogBookSignOff_LogBookEntrySection, {
        onCompleted: (response) => {
            const data = response.createLogBookSignOff_LogBookEntrySection
            updateSignOff(data)
        },
        onError: (error) => {
            console.error('Error completing safety check', error)
        },
    })

    const getFilteredFields = (fields: any, grouped = true) => {
        const logbookFields = getSlallFields()
        const defaultConfig = logbookFields.map((component: any) => component)
        var groupFields: any = []
        var nonGroupFields: any = []
        var groups: any = []
        defaultConfig.forEach((defaultLogBookComponents: any) => {
            if (
                'LogBookSignOff_LogBookComponent' ===
                defaultLogBookComponents.componentClass
            ) {
                defaultLogBookComponents.items.forEach((defaultField: any) => {
                    fields.forEach((field: any) => {
                        if (field.name === defaultField.value) {
                            if (defaultField.groupTo != undefined) {
                                groupFields.push({
                                    ...field,
                                    groupTo: defaultField.groupTo,
                                })
                                groups.includes(defaultField.groupTo)
                                    ? null
                                    : groups.push(defaultField.groupTo)
                            } else {
                                nonGroupFields.push(field)
                            }
                        }
                    })
                })
            }
        })

        if (grouped) {
            const groupedFields = groups.map((group: any) => {
                const fields = groupFields.filter(
                    (field: any) => field.groupTo === group,
                )
                return {
                    name: group,
                    field: getFieldByName(group),
                    items: fields.sort(
                        (a: any, b: any) => a.sortOrder - b.sortOrder,
                    ),
                }
            })
            return groupedFields
        }
        return nonGroupFields
            .filter(
                (field: any) =>
                    !groups?.includes(field.name) &&
                    field.name !== 'FuelEnd' &&
                    field.name !== 'EngineHours' &&
                    field.name !== 'EngineHoursEnd',
            )
            .sort((a: any, b: any) => a.sortOrder - b.sortOrder)
    }

    const isGroupVisible = (fieldName: string) => {
        const logbookFields = getSlallFields()
        const defaultConfig = logbookFields.map((component: any) => component)
        const signOff =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'LogBookSignOff_LogBookComponent',
            )
        defaultConfig.forEach((defaultLogBookComponents: any) => {
            if (
                'LogBookSignOff_LogBookComponent' ===
                defaultLogBookComponents.componentClass
            ) {
                defaultLogBookComponents.items.forEach((defaultField: any) => {
                    if (defaultField.value === fieldName) {
                        if (defaultField.groupTo) {
                            const groupField =
                                signOff[0]?.customisedComponentFields?.nodes.filter(
                                    (field: any) =>
                                        field.fieldName ===
                                        defaultField.groupTo,
                                )
                            if (groupField.length > 0) {
                                return groupField[0].status !== 'Off'
                            }
                        }
                    }
                })
            }
        })
        return false
    }

    const getFieldByName = (name: string) => {
        const logbookFields = getSlallFields()
        const defaultConfig = logbookFields.map((component: any) => component)
        var groupField: any = []
        defaultConfig.forEach((defaultLogBookComponents: any) => {
            if (
                'LogBookSignOff_LogBookComponent' ===
                defaultLogBookComponents.componentClass
            ) {
                defaultLogBookComponents.items.forEach((defaultField: any) => {
                    if (defaultField.value === name) {
                        groupField.push(defaultField)
                    }
                })
            }
        })
        return groupField[0]
    }

    const handleSave = async () => {
        if (!closeLogBookEntry) {
            toast.error('You do not have permission to close the logbook entry')
            return
        }
        // Check if master is assigned - check both masterID and master object
        const hasMaster =
            logBook?.masterID > 0 ||
            (logBook?.master && !isEmpty(logBook.master.firstName))

        if (!hasMaster) {
            toast.error('Please assign master in order to signoff the logbook')
            return
        }
        toast.dismiss()
        toast.loading('Saving Log Entry...')
        // Dismiss unrelevant comments
        const promises = unrelevantComments.map(async (comment: any) => {
            const variables = {
                id: comment.id,
                hideComment: true,
            }
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                await updateSectionMemberComment({
                    variables: {
                        input: variables,
                    },
                })
            }
        })
        await Promise.all(promises)
        const comment = sectionComment
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: 'LogBookSignOff',
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: signOff?.id,
            commentType: 'Section',
        }
        if (!selectedLocation) {
            toast.error('Please select the end location')
            return
        }
        const offlineID = currentComment?.id
            ? currentComment?.id
            : generateUniqueId()
        if (currentComment) {
            if (offline) {
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            setCurrentComment(comment)
            if (offline) {
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
                setCurrentComment('')
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
        if (
            fields.filter(
                (field: any) =>
                    field.checked === null &&
                    displayField(field.name) &&
                    isGroupVisible(field.name),
            ).length > 0
        ) {
            toast.error(
                'Please complete ' +
                    fields.filter(
                        (field: any) =>
                            field.checked === null && displayField(field.name),
                    ).length +
                    ' remaining fields',
            )
            return
        }
        if (signature === false || signature === '') {
            toast.error('Please sign the log entry')
            return
        }
        const updateVars = {
            id: signOff.id,
            fuelStart: +fuelStart,
            completedTime: time,
        }
        if (offline) {
            const data = await signOffModel.save(updateVars)
            updateSignOff(data)
            if (reloadTimer > 0) {
                setReloadTimer(reloadTimer + 1)
            }
        } else {
            updateLogBookSignOff_LogBookEntrySection({
                variables: {
                    input: updateVars,
                },
            })
        }
        updateFuelLogs()
        logOutCrew()
        const sigVariables = {
            logBookEntrySectionID: signOff?.id,
            memberID: localStorage.getItem('userId'),
            signatureData: signature,
        }
        if (offline) {
            const offlineID = generateUniqueId()
            const data = await signatureModel.save({
                ...sigVariables,
                id: offlineID,
            })
            const signOffdata = await signOffModel.save({
                id: signOff.id,
                sectionSignatureID: +data?.id,
            })
            updateSignOff(signOffdata)
            await vehiclePositionModel.save({
                id: generateUniqueId(),
                vehicleID: +vesselID,
                geoLocationID: selectedLocation.value,
                lat: !isEmpty(currentLocation?.latitude)
                    ? currentLocation?.latitude
                    : 0,
                long: !isEmpty(currentLocation?.longitude)
                    ? currentLocation?.longitude
                    : 0,
            })
            const lb: any = await logBookModel.getById(logentryID)
            const lbData: any = await logBookModel.save({
                ...lb,
                state: 'Locked',
                lockedDate: dayjs().format('YYYY-MM-DD'),
            })
            setIsLocked(true)
            if (reloadTimer > 0) {
                setReloadTimer(reloadTimer + 1)
            }
            router.back()
        } else {
            createLogBookEntrySection_Signature({
                variables: {
                    input: sigVariables,
                },
            })
        }

        if (dismissPrevComment && prevComment) {
            const variables = {
                id: prevComment.id,
                hideComment: true,
            }
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }
    const doCreateFuelLog = async (fuelTank: any) => {
        const variables = {
            fuelTankID: fuelTank.id,
            fuelAfter: fuelTank.currentLevel,
            date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            logBookEntryID: +logentryID,
        }
        if (offline) {
            const id = generateUniqueId()
            await fuelLogModel.save({
                ...variables,
                id: id,
            })
        } else {
            createFuelLog({
                variables: {
                    input: variables,
                },
            })
        }
    }

    const updateFuelLogs = async () => {
        fuelTankList &&
            (await Promise.all(
                fuelTankList?.map(async (fuelTank: any) => {
                    const variables = {
                        id: fuelTank.id,
                        currentLevel: fuelTank.currentLevel,
                    }
                    if (offline) {
                        await fuelTankModel.save(variables)
                    } else {
                        updateFuelTank({
                            variables: { input: variables },
                        })
                    }
                    logBook?.fuelLogs?.nodes?.filter(
                        (log: any) => log.fuelTankID === fuelTank.id,
                    ).length > 1
                        ? doUpdateFuelLog(fuelTank)
                        : doCreateFuelLog(fuelTank)
                }),
            ))
    }

    const doUpdateFuelLog = async (fuelTank: any) => {
        const variables = {
            id: logBook.fuelLogs.nodes
                .filter((log: any) => log.fuelTank.id === fuelTank.id)
                .sort(
                    (a: any, b: any) =>
                        new Date(b.date).getTime() - new Date(a.date).getTime(),
                )[0].id,
            fuelTankID: fuelTank.id,
            fuelAfter: fuelTank.currentLevel,
            date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            logBookEntryID: +logentryID,
        }
        if (offline) {
            await fuelLogModel.save(variables)
        } else {
            updateFuelLog({
                variables: {
                    input: variables,
                },
            })
        }
    }
    const [updateFuelLog] = useMutation(UPDATE_FUELLOG, {
        onCompleted: (response) => {
            const data = response.updateFuelLog
        },
        onError: (error) => {
            console.error('Error updating fuel log', error)
        },
    })

    const [createFuelLog] = useMutation(CREATE_FUELLOG, {
        onCompleted: (response) => {
            const data = response.createFuelLog
        },
        onError: (error) => {
            console.error('Error creating fuel log', error)
        },
    })

    const logOutCrew = () => {
        if (crew) {
            crew.filter((member: any) => member.punchOut == null).forEach(
                async (member: any) => {
                    const variables = {
                        id: member.id,
                        punchOut: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                    }
                    if (offline) {
                        await lbCrewModel.save(variables)
                    } else {
                        updateCrewMembers_LogBookEntrySection({
                            variables: { input: variables },
                        })
                    }
                },
            )
        }
    }

    const [
        updateCrewMembers_LogBookEntrySection,
        { loading: updateCrewMembers_LogBookEntrySectionLoading },
    ] = useMutation(UpdateCrewMembers_LogBookEntrySection, {
        onError: (error) => {
            console.error('updateCrewMembers_LogBookEntrySection', error)
        },
    })

    const getJsonConfig = () => {
        return JSON.stringify(logBookConfig)
    }

    const doUpdateSignOff = async (variables: any) => {
        const data = await signOffModel.save(variables)
        updateSignOff(data)
        if (reloadTimer > 0) {
            setReloadTimer(reloadTimer + 1)
        }
    }
    const [
        createLogBookEntrySection_Signature,
        { loading: createLogBookEntrySection_SignatureLoading },
    ] = useMutation(CreateLogBookEntrySection_Signature, {
        onCompleted: (response) => {
            setReloadTimer(1)
            const data = response.createLogBookEntrySection_Signature
            update_logbook_entry({
                variables: {
                    input: {
                        id: +logentryID,
                        state: 'Locked',
                        lockedDate: dayjs().format('YYYY-MM-DD'),
                        lastConfig: getJsonConfig(),
                    },
                },
            })

            updateLogBookSignOff_LogBookEntrySection({
                variables: {
                    input: {
                        id: signOff.id,
                        sectionSignatureID: +data?.id,
                    },
                },
            })

            createVesselPosition({
                variables: {
                    input: {
                        vehicleID: +vesselID,
                        geoLocationID: selectedLocation.value,
                        lat: !isEmpty(currentLocation?.latitude)
                            ? currentLocation?.latitude
                            : 0,
                        long: !isEmpty(currentLocation?.longitude)
                            ? currentLocation?.longitude
                            : 0,
                    },
                },
            })
        },
        onError: (error) => {
            console.error('Error saving signature', error)
        },
    })

    const [createVesselPosition, { loading: createVesselPositionLoading }] =
        useMutation(CREATE_VESSEL_POSITION, {
            onCompleted: (response) => {
                const data = response.createVehiclePosition
                updateLogBookSignOff_LogBookEntrySection({
                    variables: {
                        input: {
                            id: signOff.id,
                            endLocationID: +data?.id,
                        },
                    },
                })
                if (reloadTimer > 0) {
                    setReloadTimer(reloadTimer + 1)
                }
            },
            onError: (error) => {
                console.error('Error saving vessel position', error)
            },
        })

    const [update_logbook_entry, { loading: updateLogBookEntryLoading }] =
        useMutation(UPDATE_LOGBOOK_ENTRY, {
            onCompleted: (response) => {
                setIsLocked(true)
                if (reloadTimer > 0) {
                    setReloadTimer(reloadTimer + 1)
                }
                router.back()
            },
            onError: (error) => {
                toast.error('Error completing log entry')
                console.error('Error completing log entry', error)
            },
        })

    const fields = getSignoffFields(logBookConfig, signOff)

    const handleDismissPrevComment = (value: boolean) => {
        setDismissPrevComment(value)
    }

    useEffect(() => {
        if (reloadTimer > 4) {
            router.back()
        }
    }, [reloadTimer])

    useEffect(() => {
        if (prevComments) setPrevComment(prevComments[0])
    }, [prevComments])

    const handleTimeChange = (time: any) => {
        setTime(dayjs(time).format('HH:mm'))
    }

    const getEngines = async (engineIds: any) => {
        if (offline) {
            const engines = await engineModel.getByIds(engineIds)
            const data = engines.filter((engine: any) => {
                return engine.engineStartStops.nodes.some(
                    (stop: any) =>
                        +stop.logBookEntrySection.logBookEntryID ===
                        +logentryID,
                )
            })
            if (data) {
                setEngineList(data)
            }
        } else {
            await queryGetEngines({
                variables: {
                    id: engineIds,
                    filter: {
                        logBookEntryID: { eq: +logentryID },
                    },
                },
            })
        }
    }

    const [queryGetEngines] = useLazyQuery(GET_ENGINES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readEngines.nodes
            setEngineList(data)
        },
        onError: (error: any) => {
            console.error('getEngines error', error)
        },
    })

    useEffect(() => {
        if (vessel) {
            const engineIds = vessel?.parentComponent_Components?.nodes
                .filter(
                    (item: any) =>
                        item.basicComponent.componentCategory === 'Engine',
                )
                .map((item: any) => {
                    return item.basicComponent.id
                })
            engineIds?.length > 0 && getEngines(engineIds)
        }
    }, [vessel])

    const doUpdateEngineHours = async (e: any, engine: any) => {
        const variables = {
            id: engine.id,
            currentHours: +e.target.value,
        }
        const currentHours = engineList.find(
            (engine: any) => engine.id === engine.id,
        )?.currentHours
        const engineStartStop = {
            logBookEntryID: +logentryID,
            hoursStart: +currentHours,
            engineID: +engine.id,
            hoursEnd: +e.target.value,
        }
        if (offline) {
            await engineModel.save(variables)
        } else {
            updateEngineHours({
                variables: {
                    input: variables,
                },
            })
        }
        setEngineList(
            engineList.map((item: any) => {
                if (item.id === engine.id) {
                    return {
                        ...item,
                        currentHours: +e.target.value,
                    }
                }
                return item
            }),
        )
        createEngineStartStop({
            variables: {
                input: engineStartStop,
            },
        })
    }

    const [createEngineStartStop] = useMutation(CREATE_ENGINESTARTSTOP, {
        onCompleted: (response) => {
            const data = response.createEngineStartStop
            const engineIds = vessel?.parentComponent_Components?.nodes
                .filter(
                    (item: any) =>
                        item.basicComponent.componentCategory === 'Engine',
                )
                .map((item: any) => {
                    return item.basicComponent.id
                })
            engineIds?.length > 0 && getEngines(engineIds)
        },
        onError: (error) => {
            console.error('Error creating engine start/stop', error)
        },
    })

    const [updateEngineHours] = useMutation(UPDATE_ENGINE, {
        onError: (error) => {
            console.error('Error updating engine hours', error)
        },
    })

    const handleUpdateFuelTank = (tank: any, value: any) => {
        if (tank.capacity < +value) {
            toast.error(
                'Fuel level cannot be higher than tank capacity of ' +
                    tank.capacity,
            )
            return
        }
        setFuelTankList(
            fuelTankList.map((item: any) => {
                if (item.id === tank.id) {
                    item.currentLevel = +value
                    item.updatedLevel = +value
                    return item
                }
                return item
            }),
        )
    }

    const [updateFuelTank] = useMutation(UpdateFuelTank, {
        onCompleted: (response) => {
            const data = response.updateFuelTank
        },
        onError: (error) => {
            console.error('Error updating fuel tank', error)
        },
    })

    const getEngineRunHours = (engine: any) => {
        const engineStartStop = engineList.find(
            (item: any) => item.id === engine.id,
        )?.engineStartStops?.nodes
        if (engineStartStop?.length > 0) {
            const initialHours =
                engineStartStop[0].hoursStart ?? engine.currentHours
            return (engine.currentHours - initialHours).toFixed(1)
        }

        const initialHours =
            logBook?.engineStartStop?.nodes?.filter(
                (item: any) => item.engineID === engine.id,
            )?.[0]?.hoursStart ?? engine.currentHours
        return (engine.currentHours - initialHours).toFixed(1)
    }

    const getFinalFuelTankLevel = (tank: any) => {
        if (tank?.updatedLevel) {
            return tank.updatedLevel
        }
        const fuelLog = logBook?.fuelLog?.nodes
            ?.filter((log: any) => log.fuelTank.id === tank.id)
            ?.sort(
                (a: any, b: any) =>
                    new Date(b.date).getTime() - new Date(a.date).getTime(),
            )?.[0]
        if (fuelLog) {
            return +tank.currentLevel < +tank.capacity
                ? +tank.currentLevel
                : +tank.capacity
        } else {
            return +tank.currentLevel
        }
        /* return fuelLog
            ? +tank.capacity > +fuelLog.fuelAfter
                ? +fuelLog.fuelAfter
                : +tank.capacity
            : +tank.currentLevel */
    }

    const [getSectionTripReport_LogBookEntrySection] = useLazyQuery(
        TripReport_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTripReport_LogBookEntrySections.nodes
                setTripReport(data)
            },
            onError: (error: any) => {
                console.error('TripReport_LogBookEntrySection error', error)
            },
        },
    )

    const handleSetLogbook = async (logbook: any) => {
        const sectionTypes = Array.from(
            new Set(
                logbook.logBookEntrySections.nodes.map(
                    (sec: any) => sec.className,
                ),
            ),
        ).map((type) => ({
            className: type,
            ids: logbook.logBookEntrySections.nodes
                .filter((sec: any) => sec.className === type)
                .map((sec: any) => sec.id),
        }))
        sectionTypes.forEach(async (section: any) => {
            if (
                section.className === 'SeaLogs\\TripReport_LogBookEntrySection'
            ) {
                getSectionTripReport_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }
        })
    }

    const [queryLogBookEntry] = useLazyQuery(GET_LOGBOOK_ENTRY_BY_ID, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneLogBookEntry
            if (data) {
                handleSetLogbook(data)
            }
        },
        onError: (error: any) => {
            console.error('queryLogBookEntry error', error)
        },
    })

    useEffect(() => {
        queryLogBookEntry({
            variables: {
                logbookEntryId: +logentryID,
            },
        })
    }, [])

    const [creteFuelLog] = useMutation(CREATE_FUELLOG, {
        onCompleted: (response) => {
            const data = response.createFuelLog
        },
        onError: (error) => {
            console.error('Error creating fuel log', error)
        },
    })

    const [createFuelReceipts] = useMutation(CREATE_R2FILE, {
        onCompleted: (response) => {
            const data = response.createR2File
            const newReceipts = fuelReceipts.map((receipt: any) => {
                if (receipt.title === data.title) {
                    return {
                        ...receipt,
                        id: data.id,
                    }
                }
                return receipt
            })
            setFuelReceipts(newReceipts)
        },
        onError: (error) => {
            console.error('Error creating fuel receipts', error)
        },
    })

    const [createRefuellingBunkering] = useMutation(CreateRefuellingBunkering, {
        onCompleted: (response) => {
            const data = response.createRefuellingBunkering
            if (fuelReceipts.length > 0) {
                fuelReceipts.map((receipt: any) => {
                    if (!receipt.id && data.id) {
                        createFuelReceipts({
                            variables: {
                                input: {
                                    title: receipt.title,
                                    refuellingBunkeringID: data.id,
                                },
                            },
                        })
                    }
                })
            }
            creteFuelLog({
                variables: {
                    input: {
                        fuelTankID: +currentFuelTank.id,
                        fuelBefore: +currentFuelTank.currentLevel,
                        fuelAdded: +refuelling.fuelAdded,
                        fuelAfter: +refuelling.fuelAfter,
                        costPerLitre: +refuelling.costPerLitre,
                        totalCost: +refuelling.totalCost,
                        date: dayjs(refuelling?.time).format(
                            'YYYY-MM-DDTHH:mm:ss',
                        ),
                        refuellingBunkeringID: data.id,
                    },
                },
            })
            updateFuelTank({
                variables: {
                    input: {
                        id: +currentFuelTank.id,
                        currentLevel: +refuelling.fuelAfter,
                    },
                },
            })
            const tripReportID =
                tripReport.length > 0
                    ? tripReport[tripReport.length - 1].id
                    : null
            setTimeout(() => {
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'RefuellingBunkering',
                            logBookEntrySectionID: +tripReportID,
                            eventType_RefuellingBunkeringID: data.id,
                        },
                    },
                })
            }, 200)
        },
        onError: (error) => {
            console.error('Error creating refuelling', error)
        },
    })

    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            const data = response.createTripEvent
            setOpenRefuelling(false)
            handleUpdateFuelTank(currentFuelTank, refuelling.fuelAfter)
            setRefuelling({
                fuelAdded: 0,
                costPerLitre: 0,
                totalCost: 0,
            })
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
        },
    })

    const addRefuelling = async () => {
        const tripReportID =
            tripReport.length > 0 ? tripReport[tripReport.length - 1].id : null
        if (tripReportID > 0) {
            createRefuellingBunkering({
                variables: {
                    input: {
                        geoLocationID: currentRefuellingLocation?.value,
                        notes: refuelling?.content,
                        lat: currentRefuellingLocation.latitude.toString(),
                        long: currentRefuellingLocation.longitude.toString(),
                        date: dayjs(refuelling?.time).format(
                            'YYYY-MM-DDTHH:mm:ss',
                        ),
                        tripEventID: tripReportID,
                    },
                },
            })
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), tripReportID],
            })
        } else {
            const input = {
                logBookEntryID: +logentryID,
            }
            createTripReport_LogBookEntrySection({
                variables: {
                    input,
                },
            })
        }
    }

    const [createTripReport_LogBookEntrySection] = useMutation(
        CreateTripReport_LogBookEntrySection,
        {
            onCompleted: (response) => {
                const data = response.createTripReport_LogBookEntrySection
                updateTripReport({
                    id: [...tripReport.map((trip: any) => trip.id), data.id],
                })
                getSectionTripReport_LogBookEntrySection({
                    variables: {
                        id: [data.id],
                    },
                })
                createRefuellingBunkering({
                    variables: {
                        input: {
                            geoLocationID: currentRefuellingLocation?.value,
                            notes: refuelling?.content,
                            lat: currentRefuellingLocation.latitude.toString(),
                            long: currentRefuellingLocation.longitude.toString(),
                            date: dayjs(refuelling?.time).format(
                                'YYYY-MM-DDTHH:mm:ss',
                            ),
                            tripEventID: data.id,
                        },
                    },
                })
            },
            onError: (error) => {
                console.error('Error creating trip report', error)
            },
        },
    )

    const handleLocationChange = (location: any) => {
        setCurrentRefuellingLocation(location)
    }

    const handleRefuellingTimeChange = (time: any) => {
        setRefuelling({
            ...refuelling,
            time: dayjs(time).format('YYYY-MM-DDTHH:mm:ss'),
        })
    }

    const handleEditorChange = (value: string) => {
        setRefuelling({
            ...refuelling,
            content: value,
        })
    }

    return (
        <>
            <Card className="space-y-8 mb-5">
                {logBookConfig &&
                    (getFilteredFields(fields, false).filter((field: any) =>
                        displayField(field.name),
                    ).length > 0 ||
                        getFilteredFields(fields, false).filter((field: any) =>
                            displayField(field.name),
                        ).length > 0) && (
                        <div
                            key={logBookConfig.id}
                            className="border-b border-border mb-4">
                            <CheckFieldTopContent />
                            {getFilteredFields(fields, false).map(
                                (field: any, index: number) => (
                                    <DailyCheckField
                                        locked={locked}
                                        key={field.name}
                                        className="!my-2"
                                        displayField={displayField(field.name)}
                                        displayDescription={displayDescription(
                                            field.name,
                                        )}
                                        // setDescriptionPanelContent={
                                        //     setDescriptionPanelContent
                                        // }
                                        setOpenDescriptionPanel={
                                            setOpenDescriptionPanel
                                        }
                                        setDescriptionPanelHeading={
                                            setDescriptionPanelHeading
                                        }
                                        displayLabel={getFieldLabel(field.name)}
                                        inputId={field.value}
                                        handleNoChange={() =>
                                            handleHullChecks(false, field.value)
                                        }
                                        defaultNoChecked={
                                            field.checked === 'Not_Ok'
                                        }
                                        handleYesChange={() =>
                                            handleHullChecks(true, field.value)
                                        }
                                        defaultYesChecked={
                                            field.checked === 'Ok'
                                        }
                                        commentAction={() =>
                                            showCommentPopup(
                                                getComment(field.name),
                                                composeField(field.name),
                                            )
                                        }
                                        comment={
                                            getComment(field.name)?.comment
                                        }
                                        displayImage={true}
                                        fieldImages={fieldImages}
                                        onImageUpload={refreshImages}
                                    />
                                ),
                            )}
                            {getFilteredFields(fields)
                                ?.filter((groupField: any) =>
                                    displayGroupField(groupField),
                                )
                                ?.map((groupField: any) => (
                                    <div key={groupField.name}>
                                        <div>
                                            <div className="flex flex-col">
                                                {/*<H4>
                                                {groupField.field?.title
                                                    ? groupField.field.title
                                                    : groupField.field.label}
                                                {displayDescription(
                                                    groupField.name,
                                                ) && (
                                                    <Button
                                                        variant="text"
                                                        size="icon"
                                                        iconLeft={AlertCircle}
                                                        onClick={() => {
                                                            setDescriptionPanelContent(
                                                                displayDescription(
                                                                    groupField.name,
                                                                ),
                                                            )
                                                            setOpenDescriptionPanel(
                                                                true,
                                                            )
                                                            setDescriptionPanelHeading(
                                                                groupField.name,
                                                            )
                                                        }}
                                                    />
                                                )}
                                            </H4>*/}
                                                <div>
                                                    {groupField?.items?.map(
                                                        (field: any) => (
                                                            <DailyCheckField
                                                                locked={locked}
                                                                key={field.name}
                                                                displayField={displayField(
                                                                    field.name,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                )}
                                                                // setDescriptionPanelContent={
                                                                //     setDescriptionPanelContent
                                                                // }
                                                                setOpenDescriptionPanel={
                                                                    setOpenDescriptionPanel
                                                                }
                                                                setDescriptionPanelHeading={
                                                                    setDescriptionPanelHeading
                                                                }
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    handleHullChecks(
                                                                        false,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    handleHullChecks(
                                                                        true,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                                displayImage={
                                                                    true
                                                                }
                                                                fieldImages={
                                                                    fieldImages
                                                                }
                                                                onImageUpload={
                                                                    refreshImages
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                        </div>
                    )}
                <div className="grid laptop:grid-cols-2 gap-8">
                    {displayField('FuelEnd') && (
                        <>
                            {fuelTankList &&
                                fuelTankList.map((tank: any) => (
                                    <React.Fragment key={tank.id}>
                                        <Label
                                            label={`${tank.title} - fuel at end of
                                                trip`}
                                            leftContent={
                                                <SealogsFuelIcon className="size-5" />
                                            }>
                                            <div className="inline-flex w-full flex-col tablet-sm:flex-row gap-2">
                                                <Input
                                                    type="number"
                                                    // id="fuel_start"
                                                    className="w-auto min-w-48"
                                                    placeholder="Fuel end"
                                                    value={getFinalFuelTankLevel(
                                                        tank,
                                                    )}
                                                    min={0}
                                                    max={tank.capacity}
                                                    disabled={locked}
                                                    onChange={(e: any) =>
                                                        handleUpdateFuelTank(
                                                            tank,
                                                            e.target.value,
                                                        )
                                                    }
                                                    // value={fuelStart}
                                                    // onChange={(e: any) =>
                                                    //     setFuelStart(e.target.value)
                                                    // }
                                                />
                                                <Button
                                                    variant="primary"
                                                    disabled={locked}
                                                    onClick={() => {
                                                        setOpenRefuelling(true)
                                                        setCurrentFuelTank(tank)
                                                    }}>
                                                    Record refuelling /
                                                    bunkering
                                                </Button>
                                            </div>
                                        </Label>
                                    </React.Fragment>
                                ))}
                        </>
                    )}
                    {displayField('EngineHours') && engineList?.length > 0 && (
                        <>
                            {engineList?.map((engine: any) => (
                                <React.Fragment key={engine.id}>
                                    <Label
                                        label={`${engine.title} - engine hours end of trip`}
                                        htmlFor={`engine-hours-${engine.id}`}>
                                        <div className="inline-flex flex-col tablet-sm:flex-row w-full gap-2">
                                            <Input
                                                id={`engine-hours-${engine.id}`}
                                                type="number"
                                                defaultValue={
                                                    engine.currentHours
                                                }
                                                name="start"
                                                placeholder="Engine Hours"
                                                className={` !w-auto min-w-48`}
                                                disabled={locked}
                                                onBlur={(e: any) => {
                                                    doUpdateEngineHours(
                                                        e,
                                                        engine,
                                                    )
                                                }}
                                            />
                                            {displayField('EngineHoursEnd') && (
                                                <div className="flex items-center">
                                                    <span>
                                                        {/* Run hours: */}
                                                        {getFieldLabel(
                                                            'EngineHoursEnd',
                                                        )}
                                                        :
                                                    </span>
                                                    <span className="border rounded-full flex justify-center items-center w-5 h-5 md:w-9 md:h-9 p-5 ml-2">
                                                        <div className="flex-shrink-0">
                                                            {getEngineRunHours(
                                                                engine,
                                                            )}
                                                        </div>
                                                    </span>
                                                </div>
                                            )}
                                        </div>
                                    </Label>
                                </React.Fragment>
                            ))}
                        </>
                    )}
                </div>

                <Separator className="my-4" />

                <div className="grid laptop:grid-cols-2 gap-8">
                    <Label
                        label="Time trip completed"
                        className="w-full"
                        htmlFor="complete-time">
                        <TimeField
                            time={time}
                            disabled={locked}
                            handleTimeChange={handleTimeChange}
                            timeID="complete-time"
                            fieldName="Time of completion"
                            buttonLabel="Complete now"
                            hideButton={locked}
                        />
                    </Label>

                    <Label label="End Location">
                        <div className="inline-flex w-full">
                            {locked ? (
                                <Input
                                    defaultValue={selectedLocation?.label}
                                    disabled
                                />
                            ) : (
                                geoLocations && (
                                    <LocationField
                                        offline={offline}
                                        setCurrentLocation={setCurrentLocation}
                                        handleLocationChange={(value: any) => {
                                            // If value is null or undefined, return early
                                            if (!value) return

                                            // Check if the value is from dropdown selection (has 'value' property)
                                            if (value.value) {
                                                // Handle location selected from dropdown
                                                setSelectedLocation({
                                                    label: value.label,
                                                    value: value.value,
                                                    latitude: value.latitude,
                                                    longitude: value.longitude,
                                                })
                                                setCurrentLocation({
                                                    latitude: null,
                                                    longitude: null,
                                                })
                                            } else if (
                                                value.latitude !== undefined &&
                                                value.longitude !== undefined
                                            ) {
                                                // Handle direct coordinates input
                                                setSelectedLocation({
                                                    value: 0,
                                                    label: null,
                                                })
                                                setCurrentLocation({
                                                    latitude: value.latitude,
                                                    longitude: value.longitude,
                                                })
                                            }
                                        }}
                                        currentEvent={{
                                            geoLocationID:
                                                selectedLocation?.value,
                                            lat: currentLocation?.latitude,
                                            long: currentLocation?.longitude,
                                        }}
                                    />
                                )
                            )}
                        </div>
                    </Label>
                </div>

                <OpenPreviousLogbookComments
                    prevComments={prevComments}
                    onDismiss={(coms: any) => {
                        onUpdatePrevComments(coms)
                    }}
                    onDismissAll={() => onUpdatePrevComments([])}
                    enableRelevantQuestion={true}
                    onRelevantQuestionChange={(rc: any) => {
                        setUnrelevantComments(
                            rc.filter((c: any) => c.value === 'no'),
                        )
                    }}
                />

                <Label label="Shutdown log:" htmlFor="shutdown-log">
                    <Textarea
                        id={`section_comment`}
                        rows={4}
                        placeholder="Record any extra-ordinary actions taken during the shutdown in this logbook entry, including times, dates, and personnel involved. These comments will appear in the next logbook entry."
                        defaultValue={
                            getComment('LogBookSignOff', 'Section')?.comment
                        }
                        onChange={(e: any) => {
                            setSectionComment(e.target.value)
                        }}
                        disabled={locked}
                    />
                </Label>
                <Label label="Logbook completion:" htmlFor="logbook-completion">
                    <SignaturePad
                        description=" If the above the checklist is fully completed please
                        sign below."
                        locked={locked}
                        signature={isLocked ? signOff.sectionSignature : null}
                        onSignatureChanged={(sign: String) => {
                            setSignature(sign)
                        }}
                    />
                </Label>
            </Card>
            <FooterWrapper className="flex justify-end gap-2">
                {!isLocked && (
                    <Button
                        variant="back"
                        size="sm"
                        onClick={() => router.back()}
                        disabled={
                            createSectionMemberCommentLoading ||
                            updateSectionMemberCommentLoading ||
                            updateLogBookSignOff_LogBookEntrySectionLoading ||
                            createLogBookSignOff_LogBookEntrySectionLoading ||
                            updateCrewMembers_LogBookEntrySectionLoading ||
                            createVesselPositionLoading ||
                            createLogBookEntrySection_SignatureLoading ||
                            updateLogBookEntryLoading
                        }>
                        Cancel
                    </Button>
                )}
                {!imCrew && !isLocked && (
                    <Button
                        size="sm"
                        onClick={handleSave}
                        disabled={
                            createSectionMemberCommentLoading ||
                            updateSectionMemberCommentLoading ||
                            updateLogBookSignOff_LogBookEntrySectionLoading ||
                            createLogBookSignOff_LogBookEntrySectionLoading ||
                            updateCrewMembers_LogBookEntrySectionLoading ||
                            createVesselPositionLoading ||
                            createLogBookEntrySection_SignatureLoading ||
                            updateLogBookEntryLoading
                        }>
                        Complete
                    </Button>
                )}
            </FooterWrapper>
            <AlertDialogNew
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={handleSaveComment}
                noButton={locked}
                actionText="Save">
                <Label label="Comment" htmlFor="comment">
                    <Textarea
                        id="comment"
                        rows={4}
                        placeholder="Comment"
                        defaultValue={
                            currentComment ? currentComment.comment : ''
                        }
                        disabled={locked}
                    />
                </Label>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openRefuelling}
                setOpenDialog={setOpenRefuelling}
                size="xl"
                handleCreate={() => {
                    addRefuelling()
                }}
                title="Record refuelling / bunkering"
                actionText="Save">
                <Label
                    label={
                        bp.phablet
                            ? 'Location where Refuelling / Bunkering takes place'
                            : 'Refuelling Location'
                    }
                    htmlFor="refuelling-location"
                    className="mb-4 text-wrap">
                    <LocationField
                        offline={offline}
                        setCurrentLocation={setCurrentRefuellingLocation}
                        handleLocationChange={handleLocationChange}
                    />
                </Label>
                <div className="grid grid-cols-1 xs:grid-cols-2 gap-4">
                    <Label
                        label="Fuel before"
                        htmlFor="fuel-before"
                        className="w-full">
                        <Input
                            id="fuel-before"
                            type="number"
                            min="0"
                            readOnly
                            max={currentFuelTank?.capacity}
                            value={currentFuelTank?.currentLevel}
                            className="w-full"
                        />
                    </Label>
                    <Label
                        label="Fuel added"
                        htmlFor="fuel-added"
                        className="w-full">
                        <Input
                            id="fuel-added"
                            type="number"
                            min="0"
                            max={currentFuelTank?.capacity}
                            value={refuelling?.fuelAdded ?? 0}
                            onChange={(e) =>
                                setRefuelling({
                                    ...refuelling,
                                    fuelAdded: e.target.value,
                                    fuelAfter:
                                        +currentFuelTank?.currentLevel +
                                        +e.target.value,
                                    totalCost:
                                        +e.target.value *
                                        +refuelling?.costPerLitre,
                                })
                            }
                            className="w-full"
                        />
                    </Label>
                    <Label
                        label="Fuel after"
                        htmlFor="fuel-after"
                        className="w-full">
                        <Input
                            id="fuel-after"
                            type="number"
                            min="0"
                            max={currentFuelTank?.capacity}
                            value={
                                refuelling?.fuelAfter ??
                                currentFuelTank?.currentLevel
                            }
                            readOnly
                            className="w-full"
                        />
                    </Label>
                    <Label
                        label="Cost per litre"
                        htmlFor="cost-per-litre"
                        className="w-full">
                        <Input
                            id="cost-per-litre"
                            type="number"
                            min="0"
                            value={refuelling?.costPerLitre ?? 0}
                            onChange={(e) =>
                                setRefuelling({
                                    ...refuelling,
                                    costPerLitre: e.target.value,
                                    totalCost:
                                        +refuelling?.fuelAdded *
                                        +e.target.value,
                                })
                            }
                            className="w-full"
                        />
                    </Label>
                    <Label
                        label="Total cost"
                        htmlFor="total-cost"
                        className="w-full xs:col-span-2 tablet-sm:col-span-1">
                        <Input
                            id="total-cost"
                            type="number"
                            min="0"
                            value={refuelling?.totalCost}
                            readOnly
                            className="w-full"
                        />
                    </Label>
                </div>
                <Label
                    label="Refuelling time"
                    htmlFor="fuel-added-time"
                    className="mt-4">
                    <TimeField
                        time={
                            refuelling?.time
                                ? dayjs(refuelling?.time).format('HH:mm')
                                : dayjs().format('HH:mm')
                        }
                        handleTimeChange={handleRefuellingTimeChange}
                        timeID="fuel-added-time"
                        fieldName="Time"
                    />
                </Label>
                <Label label="Fuel Receipts / Comments" className="mt-4">
                    <UploadCloudFlare
                        files={fuelReceipts}
                        setFiles={setFuelReceipts}
                    />
                </Label>
                <Label
                    label="Fuel Receipts comment"
                    htmlFor="fuel-receipts"
                    className="mt-4">
                    <Textarea
                        id="fuel-receipts"
                        name="fuel-receipts"
                        rows={4}
                        placeholder="Fuel Receipts comment"
                        defaultValue={refuelling?.content}
                        onChange={(e) => handleEditorChange(e.target.value)}
                    />
                </Label>
            </AlertDialogNew>
            <Sheet
                open={openDescriptionPanel}
                onOpenChange={(open) => {
                    setOpenDescriptionPanel(open)
                    if (!open) {
                        setDescriptionPanelContent('')
                        setDescriptionPanelHeading('')
                    }
                }}>
                <SheetContent side="left">
                    {openDescriptionPanel && (
                        <div>
                            <SheetHeader>
                                <SheetTitle>
                                    <span>
                                        Field -{' '}
                                        <span className="font-light">
                                            {descriptionPanelHeading}
                                        </span>
                                    </span>
                                    <X
                                        onClick={() => {
                                            setOpenDescriptionPanel(false)
                                            setDescriptionPanelContent('')
                                            setDescriptionPanelHeading('')
                                        }}
                                    />
                                </SheetTitle>
                            </SheetHeader>
                            <div>
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: descriptionPanelContent,
                                    }}></div>
                            </div>
                        </div>
                    )}
                </SheetContent>
            </Sheet>
        </>
    )
}
