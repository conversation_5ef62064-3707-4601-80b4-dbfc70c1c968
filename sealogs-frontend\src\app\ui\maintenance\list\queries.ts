import gql from 'graphql-tag'

// GET_MAINTENANCE_CHECK_LIST
export const ReadComponentMaintenanceCheckList = gql`
    query ReadComponentMaintenanceCheckList(
        $inventoryID: Int!
        $vesselID: Int!
        $archived: Int = 0
    ) {
        readComponentMaintenanceCheckList(
            inventoryID: $inventoryID
            vesselID: $vesselID
            archived: $archived
        ) {
            list
        }
    }
`

// GET_CREW_BY_IDS
export const ReadSeaLogsMembers = gql`
    query ReadSeaLogsMembers($crewMemberIDs: [ID]) {
        readSeaLogsMembers(
            filter: { id: { in: $crewMemberIDs }, isArchived: { eq: false } }
        ) {
            nodes {
                id
                archived
                isArchived
                phoneNumber
                alternatePhoneNumber
                isMaster
                isCrew
                isPilot
                isTransferee
                username
                dashboardVessels
                viewArchivedMode
                requireMFA
                firstName
                surname
                email
                seaLogsTheme
                defaultRegisteredMethodID
                hasSkippedMFARegistration
                accountResetHash
                accountResetExpired
                clientID
                trainingSessionsDue {
                    nodes {
                        id
                        dueDate
                        memberID
                        member {
                            id
                            firstName
                            surname
                        }
                        vesselID
                        vessel {
                            id
                            title
                        }
                        trainingTypeID
                        trainingType {
                            id
                            title
                        }
                    }
                }
                primaryDuty {
                    id
                    archived
                    title
                    abbreviation
                }
                currentVehicleID
                className
                lastEdited
                crewTraining_LogBookEntrySections {
                    nodes {
                        id
                    }
                }
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
                logBookEntries {
                    nodes {
                        id
                    }
                }
                groups {
                    nodes {
                        id
                    }
                }
                departments {
                    nodes {
                        id
                        title
                    }
                }
            }
        }
    }
`

export const ReadVessels = gql`
    query ReadVessels(
        $limit: Int
        $offset: Int
        $filter: VesselFilterFields = {}
        $entryFilter: LogBookEntryFilterFields = {
            state: { in: [Editing, Reopened] }
        }
    ) {
        readVessels(filter: $filter, limit: $limit, offset: $offset) {
            nodes {
                id
                archived
                title
                registration
                callSign
                icon
                iconMode
                photoID
                minCrew
                showOnDashboard
                vesselType
                logBookID
                vehiclePositions(sort: { created: DESC }, limit: 1) {
                    nodes {
                        id
                        lat
                        long
                        geoLocation {
                            id
                            title
                            lat
                            long
                        }
                    }
                }
                parentComponent_Components {
                    nodes {
                        basicComponent {
                            id
                            title
                            componentCategory
                        }
                        parentComponent {
                            id
                            title
                        }
                    }
                }
                logBookEntries(filter: $entryFilter, limit: 1) {
                    nodes {
                        id
                    }
                }
                componentMaintenanceChecks {
                    nodes {
                        archived
                        name
                        expires
                        status
                        completed
                        maintenanceSchedule {
                            __typename
                        }
                    }
                }
            }
        }
    }
`
