'use client'

import React, { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useLazyQuery, useMutation } from '@apollo/client'
import dayjs from 'dayjs'
import { isEmpty, trim } from 'lodash'
import {
    CREATE_INCIDENT_RECORD,
    UPDATE_INCIDENT_RECORD,
    DELETE_INCIDENT_RECORDS,
} from './graphql/mutations'
import { GET_INCIDENT_RECORD } from './graphql/queries'
import { CreateTripEvent, UpdateTripEvent } from '@/app/lib/graphQL/mutation'
import TripEventModel from '@/app/offline/models/tripEvent'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import DateField from '@/components/ui/date-field'
import TimeField from '@/app/ui/logbook/components/time'
import LocationField from '@/app/ui/logbook/components/location/location'
import CrewMultiSelectDropdown from '@/app/ui/crew/multiselect-dropdown/multiselect-dropdown'
import Loading from '@/app/loading'
import UploadCloudFlare from '@/app/ui/logbook/components/upload-cf'
import { CREATE_R2FILE } from '@/app/lib/graphQL/mutation'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { toast } from 'sonner'
import { Combobox } from '@/components/ui/comboBox'
import { H1, H2, H3, P } from '@/components/ui/typography'
import { ArrowLeft, Check } from 'lucide-react'
import VesselDropdown from '@/components/filter/components/vessel-dropdown'
import CrewDropdown from '@/components/filter/components/crew-dropdown/crew-dropdown'
import Editor from '../editor'
import { useQueryState } from 'nuqs'
import { CheckFieldLabel } from '@/components/ui/check-field-label'
import UploadCloudFlareCaptures, {
    getCloudFlareImagesFile,
} from '../logbook/components/upload-images'
import { GET_SECTION_MEMBER_IMAGES } from '@/app/lib/graphQL/query'
import CloudFlareCaptures from '../logbook/components/CloudFlareCaptures'

interface IncidentRecordFormProps {
    id?: number
    inLogbook?: boolean
    closeModal?: () => void
    updateTripReport?: any
    currentTrip?: any
    selectedEvent?: any
    offline?: boolean
    tripReport?: any
}

const IncidentRecordForm: React.FC<IncidentRecordFormProps> = ({
    id = 0,
    inLogbook = false,
    closeModal,
    updateTripReport,
    currentTrip,
    selectedEvent,
    offline = false,
    tripReport = [],
}) => {
    const router = useRouter()
    const searchParams = useSearchParams()
    // Get recordId from URL, props, or selectedEvent
    const recordId = searchParams.get('id')
        ? parseInt(searchParams.get('id') as string)
        : id
    const urlVesselId = searchParams.get('vesselID')

    // Offline models
    const tripEventModel = new TripEventModel()

    // Form state
    const [title, setTitle] = useState<string>('')
    const [vesselId, setVesselId] = useState<string | null>(urlVesselId || null)
    const [startDate, setStartDate] = useState<any>(dayjs())
    const [startTime, setStartTime] = useState<any>(dayjs().format('HH:mm'))
    const [endDate, setEndDate] = useState<any>(dayjs())
    const [endTime, setEndTime] = useState<any>(dayjs().format('HH:mm'))
    const [locationId, setLocationId] = useState<string | null>(null)
    const [incidentType, setIncidentType] = useState<string>('incident')
    const [personsInvolved, setPersonsInvolved] = useState<number>(0)
    const [reportedById, setReportedById] = useState<string | null>(null)
    const [description, setDescription] = useState<string>('')
    const [treatment, setTreatment] = useState<string>('none')
    const [contributingFactor, setContributingFactor] = useState<string>('none')
    const [riskAssessmentReviewed, setRiskAssessmentReviewed] =
        useState<boolean>(false)
    const [notifiable, setNotifiable] = useState<boolean>(false)
    const [membersToNotify, setMembersToNotify] = useState<string[]>([])

    // UI state
    const [loading, setLoading] = useState<boolean>(false) // Start with loading false
    const [openDeleteConfirmation, setOpenDeleteConfirmation] =
        useState<boolean>(false)
    const [attachments, setAttachments] = useState<any[]>([])

    const [activeTab] = useQueryState('tab')
    const [closeOnSave, setCloseOnSave] = useState(false)

    const [createIncidentRecord, { loading: createLoading }] = useMutation(
        CREATE_INCIDENT_RECORD,
        {
            onCompleted: async (response) => {
                const newRecordId = response.createIncidentRecord.id

                // Create trip event for the new incident record when in logbook context
                if (inLogbook && currentTrip?.id) {
                    if (offline) {
                        // Create trip event offline
                        const tripEventData = await tripEventModel.save({
                            id: generateUniqueId(),
                            eventCategory: 'IncidentRecord',
                            logBookEntrySectionID: currentTrip.id,
                            incidentRecordID: newRecordId,
                        })
                    } else {
                        // Create trip event online
                        createTripEvent({
                            variables: {
                                input: {
                                    eventCategory: 'IncidentRecord',
                                    logBookEntrySectionID: currentTrip.id,
                                    incidentRecordID: newRecordId,
                                },
                            },
                        })
                    }

                    // Update trip report
                    if (updateTripReport) {
                        updateTripReport({
                            id: [
                                ...tripReport.map((trip: any) => trip.id),
                                currentTrip.id,
                            ],
                        })
                    }
                }

                // Create attachments for the new record
                if (attachments.length > 0 && newRecordId) {
                    attachments.forEach((attachment: any) => {
                        if (!attachment.id) {
                            createAttachments({
                                variables: {
                                    input: {
                                        title: attachment.title,
                                        incidentRecordID: newRecordId,
                                    },
                                },
                            })
                        }
                    })
                }

                if (activeTab == 'trip-log') {
                    if (closeOnSave) {
                        setCloseOnSave(false)
                        closeModal?.()
                    }
                } else {
                    router.push('/incident-records')
                }
            },
            onError: (error) => {
                console.error(`Error creating incident record: `, error)
                toast.error(`Error creating incident record: ${error.message}`)
            },
        },
    )

    const [updateIncidentRecord, { loading: updateLoading }] = useMutation(
        UPDATE_INCIDENT_RECORD,
        {
            onCompleted: async () => {
                // Update trip event if in logbook context and selectedEvent exists
                if (inLogbook && selectedEvent?.id && currentTrip?.id) {
                    if (offline) {
                        // Update trip event offline
                        await tripEventModel.save({
                            id: selectedEvent.id,
                            eventCategory: 'IncidentRecord',
                            logBookEntrySectionID: currentTrip.id,
                            incidentRecordID: recordId,
                        })
                    } else {
                        // Update trip event online
                        updateTripEvent({
                            variables: {
                                input: {
                                    id: selectedEvent.id,
                                    eventCategory: 'IncidentRecord',
                                    logBookEntrySectionID: currentTrip.id,
                                    incidentRecordID: recordId,
                                },
                            },
                        })
                    }

                    // Update trip report
                    if (updateTripReport) {
                        updateTripReport({
                            id: [
                                ...tripReport.map((trip: any) => trip.id),
                                currentTrip.id,
                            ],
                        })
                    }
                }

                // Process any new attachments
                if (attachments.length > 0) {
                    attachments.forEach((attachment: any) => {
                        if (!attachment.id) {
                            createAttachments({
                                variables: {
                                    input: {
                                        title: attachment.title,
                                        incidentRecordID: recordId,
                                    },
                                },
                            })
                        }
                    })
                }

                if (activeTab == 'trip-log') {
                    if (closeOnSave) {
                        setCloseOnSave(false)
                        closeModal?.()
                    }
                } else {
                    router.push('/incident-records')
                }
            },
            onError: (error) => {
                toast.error(`Error updating incident record: ${error.message}`)
            },
        },
    )

    const [deleteIncidentRecords, { loading: deleteLoading }] = useMutation(
        DELETE_INCIDENT_RECORDS,
        {
            onCompleted: () => {
                setOpenDeleteConfirmation(false)

                if (activeTab == 'trip-log') {
                    closeModal?.()
                } else {
                    router.push('/incident-records')
                }
            },
            onError: (error) => {
                setOpenDeleteConfirmation(false)
                toast.error(`Error deleting incident record: ${error.message}`)
            },
        },
    )

    // Create attachments mutation
    const [createAttachments, { loading: attachmentsLoading }] = useMutation(
        CREATE_R2FILE,
        {
            onCompleted: (response) => {
                const data = response.createR2File
                const newAttachments = attachments.map((attachment: any) => {
                    if (attachment.title === data.title) {
                        return {
                            ...attachment,
                            id: data.id,
                        }
                    }
                    return attachment
                })
                setAttachments(newAttachments)
            },
            onError: (error) => {
                console.error('Error creating attachments', error)
                toast.error(`Error creating attachments: ${error.message}`)
            },
        },
    )

    // Trip event mutations
    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {},
        onError: (error) => {
            console.error('Error creating trip event:', error)
        },
    })

    const [updateTripEvent] = useMutation(UpdateTripEvent, {
        onCompleted: (response) => {},
        onError: (error) => {
            console.error('Error updating trip event:', error)
        },
    })

    // GraphQL query to fetch incident record for editing
    const [getIncidentRecord] = useLazyQuery(GET_INCIDENT_RECORD, {
        fetchPolicy: 'network-only',
        onCompleted: (data) => {
            if (data && data.readOneIncidentRecord) {
                const record = data.readOneIncidentRecord

                // Parse dates and times from the format "YYYY-MM-DD HH:mm"
                if (record.startDate) {
                    // Parse the date and extract just the date part
                    setStartDate(dayjs(record.startDate))
                    // Format the time to HH:mm using dayjs
                    setStartTime(dayjs(record.startDate).format('HH:mm'))
                }

                if (record.endDate) {
                    // Parse the date and extract just the date part
                    setEndDate(dayjs(record.endDate))
                    // Format the time to HH:mm using dayjs
                    setEndTime(dayjs(record.endDate).format('HH:mm'))
                }

                // Set other form fields
                setTitle(record.title || '')
                // Set locationId as a string (not a number)
                if (record.location?.id) {
                    // Store the location ID as a string to match exactly what's in the geoLocations array
                    setLocationId(record.location.id)
                } else {
                    setLocationId(null)
                }
                setVesselId(record.vessel?.id || null)
                setIncidentType(record.incidentType || 'incident')
                setPersonsInvolved(record.personsInvolved || 0)
                // Ensure reportedById is stored as a string to match CrewDropdown expectations
                setReportedById(
                    record.reportedBy?.id ? String(record.reportedBy.id) : null,
                )
                setDescription(record.description || '')
                setTreatment(record.treatment || 'none')
                setContributingFactor(record.contributingFactor || 'none')
                setRiskAssessmentReviewed(
                    record.riskAssessmentReviewed || false,
                )
                setNotifiable(record.notifiable || false)

                // Set members to notify
                if (record.membersToNotify && record.membersToNotify.nodes) {
                    setMembersToNotify(
                        record.membersToNotify.nodes.map(
                            (member: any) => member.id,
                        ),
                    )
                }

                // Set attachments
                if (record.attachments && record.attachments.nodes) {
                    setAttachments(record.attachments.nodes)
                }

                setLoading(false)
            }
        },
        onError: (error) => {
            toast.error(`Error fetching incident record: ${error.message}`)
            setLoading(false)
        },
    })

    // Load incident record data if editing
    useEffect(() => {
        // Check if we have a valid recordId from props or from selectedEvent
        let effectiveRecordId = recordId

        // If recordId is 0, try to get it from selectedEvent
        if (effectiveRecordId === 0 && selectedEvent) {
            effectiveRecordId = selectedEvent.incidentRecordID || 0
        }

        if (effectiveRecordId > 0) {
            // Only set loading to true for edit forms
            setLoading(true)
            getIncidentRecord({ variables: { id: effectiveRecordId } })
        }
        // For new forms, we keep loading as false
    }, [recordId, selectedEvent, getIncidentRecord])

    // Set vessel ID from URL if in logbook context
    useEffect(() => {
        if (inLogbook && currentTrip?.vesselID) {
            setVesselId(currentTrip.vesselID.toString())
        } else if (inLogbook) {
            // If we don't have currentTrip.vesselID, try to get it from the URL
            // const urlVesselId = searchParams.get('vesselID')
            if (urlVesselId) {
                setVesselId(urlVesselId)
            }
        }
    }, [inLogbook, currentTrip, searchParams])

    // Handle form submission
    const handleSubmit = () => {
        // Check if title is empty
        if (isEmpty(trim(title))) {
            toast.error('Title is required')
            return
        }

        // Combine date and time in the format "YYYY-MM-DD HH:mm"
        const startDateTime = `${dayjs(startDate).format('YYYY-MM-DD')} ${startTime}`
        const endDateTime = `${dayjs(endDate).format('YYYY-MM-DD')} ${endTime}`

        // Ensure we have a vessel ID if in logbook context
        if (inLogbook && !vesselId && currentTrip?.vesselID) {
            setVesselId(currentTrip.vesselID.toString())
        } else if (inLogbook && !vesselId) {
            // const urlVesselId = searchParams.get('vesselID')
            if (urlVesselId) {
                setVesselId(urlVesselId)
            } else {
                console.error('Vessel ID is required')
                return
            }
        }

        const formData = {
            title: title,
            startDate: startDateTime,
            endDate: endDateTime,
            locationID: locationId, // This is a string ID from the database
            vesselID: urlVesselId && +urlVesselId > 0 ? +urlVesselId : vesselId,
            incidentType: incidentType,
            personsInvolved: personsInvolved,
            reportedByID: reportedById,
            description: description,
            treatment: treatment,
            contributingFactor: contributingFactor,
            riskAssessmentReviewed: riskAssessmentReviewed,
            notifiable: notifiable,
            membersToNotify: membersToNotify.join(','),
        }

        if (recordId > 0) {
            // Update existing record
            updateIncidentRecord({
                variables: {
                    input: {
                        id: recordId,
                        ...formData,
                    },
                },
            })
        } else {
            // Create new record
            createIncidentRecord({
                variables: {
                    input: formData,
                },
            })
        }
    }

    // Handle delete
    const handleDelete = () => {
        if (recordId > 0) {
            deleteIncidentRecords({
                variables: {
                    ids: [recordId],
                },
            })
        }
    }

    // Incident type options
    const incidentTypeOptions = [
        { value: 'accident', label: 'Accident' },
        { value: 'incident', label: 'Incident' },
        { value: 'nearMiss', label: 'Near Miss' },
        { value: 'safetyEvent', label: 'Safety Event' },
        { value: 'medical', label: 'Illness or Injury (Medical)' },
        { value: 'assistanceRendered', label: 'Assistance Rendered' },
        {
            value: 'mental',
            label: 'Behavioural Challenge / Mental Health and Wellness',
        },
        { value: 'other', label: 'Other' },
    ]

    // Treatment options
    const treatmentOptions = [
        { value: 'none', label: 'No Treatment' },
        { value: 'firstAidOnboard', label: 'First Aid Onboard' },
        { value: 'firstAidOnshore', label: 'First Aid Onshore' },
        { value: 'restrictedWorkInjury', label: 'Restricted Work Injury' },
        { value: 'lostTimeInjury', label: 'Lost Time Injury' },
        { value: 'doctorOrHospital', label: 'Doctor or Hospital' },
        { value: 'fatality', label: 'Fatality' },
        { value: 'other', label: 'Other' },
    ]

    // Contributing factor options
    const contributingFactorOptions = [
        { value: 'none', label: 'None' },
        { value: 'humanError', label: 'Human Error' },
        { value: 'equipmentProcedure', label: 'Equipment Procedure' },
        { value: 'environment', label: 'Environment' },
        { value: 'other', label: 'Other' },
    ]

    // Show loading component only when retrieving data
    if (loading) {
        return <Loading message={`Loading incident record...`} />
    }

    return (
        <>
            {!inLogbook && (
                <H1>{recordId > 0 ? 'Edit ' : 'New '} Incident Record</H1>
            )}
            <div className="py-5 space-y-8">
                <div>
                    {!inLogbook && (
                        <H3 className="mb-4">
                            Incident – Accident – Near Miss – Safety Event
                        </H3>
                    )}
                    <P className="mb-6">
                        This form is designed to be simple and practical enough
                        to be completed during or shortly after a high-stress
                        situation. It does not replace any official reporting
                        forms required by Authorities, nor does it substitute
                        your internal documentation or investigation processes.
                    </P>
                </div>

                <div className="space-y-8">
                    <Label htmlFor="title" label="Title">
                        <Input
                            id="title"
                            type="text"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                            placeholder="Enter incident title"
                        />
                    </Label>
                    {urlVesselId && +urlVesselId === 0 && (
                        <Label htmlFor="vessel" label="Vessel">
                            <VesselDropdown
                                value={vesselId}
                                onChange={(option: any) =>
                                    setVesselId(option?.value || null)
                                }
                                placeholder="Select Vessel"
                                isClearable={true}
                            />
                        </Label>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <Label htmlFor="start-date" label="Start date">
                            <DateField
                                date={startDate}
                                handleDateChange={setStartDate}
                                dateID="start-date"
                                fieldName="Start date"
                            />
                        </Label>

                        <Label htmlFor="start-time" label="Start time">
                            <TimeField
                                time={startTime}
                                handleTimeChange={(newTime: any) => {
                                    // Use dayjs to format the time to HH:mm
                                    setStartTime(dayjs(newTime).format('HH:mm'))
                                }}
                                timeID="start-time"
                                fieldName="Start time"
                            />
                        </Label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <Label htmlFor="end-date" label="End date">
                            <DateField
                                date={endDate}
                                handleDateChange={setEndDate}
                                dateID="end-date"
                                fieldName="End date"
                            />
                        </Label>

                        <Label htmlFor="end-time" label="End time">
                            <TimeField
                                time={endTime}
                                handleTimeChange={(newTime: any) => {
                                    // Use dayjs to format the time to HH:mm
                                    setEndTime(dayjs(newTime).format('HH:mm'))
                                }}
                                timeID="end-time"
                                fieldName="End time"
                            />
                        </Label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <Label htmlFor="location" label="Location">
                            <LocationField
                                handleLocationChange={(value: any) => {
                                    // If value is null or undefined, clear the location
                                    if (!value) {
                                        setLocationId(null)
                                        return
                                    }

                                    // Check if the value is from dropdown selection (has 'value' property)
                                    if (value.value) {
                                        // Handle location selected from dropdown
                                        setLocationId(value.value)
                                    } else if (
                                        value.latitude !== undefined &&
                                        value.longitude !== undefined
                                    ) {
                                        // For incident records, we only support location IDs, not coordinates
                                        // If coordinates are provided, we clear the location ID
                                        setLocationId(null)
                                    }
                                }}
                                setCurrentLocation={() => {}}
                                currentEvent={{
                                    geoLocationID: locationId,
                                }}
                                showAddNewLocation={false}
                                showUseCoordinates={false}
                                showCurrentLocation={false}
                            />
                        </Label>

                        <Label htmlFor="incident-type" label="Incident Type">
                            <Combobox
                                id="incident-type"
                                options={incidentTypeOptions}
                                value={
                                    incidentTypeOptions.find(
                                        (option) =>
                                            option.value === incidentType,
                                    ) || null
                                }
                                onChange={(option: any) =>
                                    setIncidentType(option?.value || 'incident')
                                }
                                placeholder="Select Incident Type"
                            />
                        </Label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <Label
                            htmlFor="persons-involved"
                            label="Person(s) Involved">
                            <Input
                                id="persons-involved"
                                type="number"
                                value={personsInvolved}
                                onChange={(e) =>
                                    setPersonsInvolved(
                                        parseInt(e.target.value) || 0,
                                    )
                                }
                                min="0"
                            />
                        </Label>

                        <Label htmlFor="reported-by" label="Reported By">
                            <CrewDropdown
                                value={reportedById}
                                onChange={(option: any) => {
                                    // Handle both cases: when option is an object with value property
                                    // or when option is just the crew ID (due to CrewDropdown bug)
                                    if (
                                        typeof option === 'object' &&
                                        option?.value
                                    ) {
                                        setReportedById(String(option.value))
                                    } else if (
                                        typeof option === 'string' ||
                                        typeof option === 'number'
                                    ) {
                                        setReportedById(String(option))
                                    } else {
                                        setReportedById(null)
                                    }
                                }}
                                placeholder="Select Crew Member"
                                isClearable={true}
                                vesselID={vesselId ? parseInt(vesselId) : 0}
                            />
                        </Label>
                    </div>

                    <Label htmlFor="description" label="Description">
                        <Editor
                            id="description"
                            placeholder="Provide a detailed narrative (include info on treatment refusals, police or emergency service feedback, etc.)"
                            className="w-full"
                            content={description}
                            handleEditorChange={(content: any) => {
                                setDescription(content)
                            }}
                            autoResize
                        />
                    </Label>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <Label htmlFor="treatment" label="Treatment">
                            <Combobox
                                id="treatment"
                                options={treatmentOptions}
                                value={
                                    treatmentOptions.find(
                                        (option) => option.value === treatment,
                                    ) || null
                                }
                                onChange={(option: any) =>
                                    setTreatment(option?.value || 'none')
                                }
                                placeholder="Select Treatment"
                            />
                        </Label>

                        <Label
                            htmlFor="contributing-factor"
                            label="Contributing Factor">
                            <Combobox
                                id="contributing-factor"
                                options={contributingFactorOptions}
                                value={
                                    contributingFactorOptions.find(
                                        (option) =>
                                            option.value === contributingFactor,
                                    ) || null
                                }
                                onChange={(option: any) =>
                                    setContributingFactor(
                                        option?.value || 'none',
                                    )
                                }
                                placeholder="Select Contributing Factor"
                            />
                        </Label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <CheckFieldLabel
                            id="risk-assessment"
                            isRadioStyle
                            variant="warning"
                            label="Risk Assessment Reviewed"
                            checked={riskAssessmentReviewed}
                            onCheckedChange={(checked) =>
                                setRiskAssessmentReviewed(checked === true)
                            }
                        />

                        <CheckFieldLabel
                            id="notifiable"
                            isRadioStyle
                            variant="warning"
                            label="Notifiable to Authorities"
                            checked={notifiable}
                            onCheckedChange={(checked) =>
                                setNotifiable(checked === true)
                            }
                        />
                    </div>

                    <Label
                        htmlFor="team-members"
                        label="Team Members to Notify">
                        <CrewMultiSelectDropdown
                            value={membersToNotify}
                            onChange={(selected: any) => {
                                setMembersToNotify(
                                    selected.map((item: any) => item.value),
                                )
                            }}
                        />
                    </Label>

                    <Label htmlFor="attachments" label="Attachments">
                        {/* <UploadCloudFlare
                            files={attachments}
                            setFiles={setAttachments}
                        /> */}
                    </Label>
                    <div className="w-full flex flex-col space-y-2">
                        <CloudFlareCaptures
                            inputId={selectedEvent?.id || recordId || 0}
                            sectionId={currentTrip.id}
                            buttonType={'button'}
                            sectionName={'tripEventID'}
                        />
                    </div>

                    <div className="flex justify-end space-x-3">
                        <Button
                            variant="back"
                            iconLeft={ArrowLeft}
                            onClick={
                                activeTab == 'trip-log'
                                    ? closeModal
                                    : () => router.push('/incident-records')
                            }
                            disabled={
                                createLoading ||
                                updateLoading ||
                                deleteLoading ||
                                attachmentsLoading
                            }>
                            Cancel
                        </Button>
                        {recordId > 0 && (
                            <Button
                                variant="destructive"
                                onClick={() => setOpenDeleteConfirmation(true)}
                                disabled={
                                    createLoading ||
                                    updateLoading ||
                                    deleteLoading ||
                                    attachmentsLoading
                                }>
                                Delete
                            </Button>
                        )}
                        <Button
                            variant="primary"
                            onClick={() => {
                                setCloseOnSave(true)
                                handleSubmit()
                            }}
                            iconRight={Check}
                            disabled={
                                createLoading ||
                                updateLoading ||
                                deleteLoading ||
                                attachmentsLoading
                            }
                            isLoading={createLoading || updateLoading}>
                            {recordId > 0 ? 'Update' : 'Save'}
                        </Button>
                    </div>
                </div>
            </div>

            <AlertDialogNew
                openDialog={openDeleteConfirmation}
                setOpenDialog={setOpenDeleteConfirmation}
                title="Delete Incident Record"
                description="Are you sure you want to delete this incident record? This action cannot be undone."
                cancelText="Cancel"
                destructiveActionText="Delete"
                handleDestructiveAction={handleDelete}
                showDestructiveAction={true}
                variant="warning"
                destructiveLoading={deleteLoading}>
                {deleteLoading && <>Deleting incident record...</>}
            </AlertDialogNew>
        </>
    )
}

export default IncidentRecordForm
