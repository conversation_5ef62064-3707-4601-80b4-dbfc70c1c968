'use client'
import dayjs from 'dayjs'
import React, { useEffect, useState, useCallback } from 'react'

import {
    CreateEventType_PassengerDropFacility,
    UpdateEventType_PassengerDropFacility,
    CreateTripEvent,
    UpdateTripEvent,
    CREATE_GEO_LOCATION,
    UpdateTripReport_LogBookEntrySection,
    UPDATE_FUELLOG,
    CREATE_FUELLOG,
    UpdateFuelTank,
} from '@/app/lib/graphQL/mutation'
import {
    GET_FUELLOGS,
    GET_FUELTANKS,
    GetTripEvent,
} from '@/app/lib/graphQL/query'
import { Combobox } from '@/components/ui/comboBox'
import { useLazyQuery, useMutation } from '@apollo/client'
import { toast } from 'sonner'

import LocationField from '../components/location/location'
import TimeField from '../components/time'
import { SealogsFuelIcon } from '@/app/lib/icons/SealogsFuelIcon'
import FuelTankModel from '@/app/offline/models/fuelTank'
import TripEventModel from '@/app/offline/models/tripEvent'
import EventType_PassengerDropFacilityModel from '@/app/offline/models/eventType_PassengerDropFacility'
import GeoLocationModel from '@/app/offline/models/geoLocation'
import TripReport_LogBookEntrySectionModel from '@/app/offline/models/tripReport_LogBookEntrySection'
import FuelLogModel from '@/app/offline/models/fuelLog'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { AlertDialogNew, Button } from '@/components/ui'
import { ArrowLeft, Check } from 'lucide-react'

export default function PassengerDropFacility({
    geoLocations,
    currentTrip = false,
    updateTripReport,
    selectedEvent = false,
    tripReport,
    closeModal,
    type,
    inLogbook = false,
    logBookConfig,
    previousDropEvent,
    vessel,
    locked,
    offline = false,
    fuelLogs,
}: {
    geoLocations: any
    currentTrip: any
    updateTripReport: any
    selectedEvent: any
    tripReport: any
    closeModal: any
    inLogbook?: boolean
    type: any
    logBookConfig: any
    previousDropEvent: any
    vessel: any
    locked: any
    offline?: boolean
    fuelLogs?: any
}) {
    const [locations, setLocations] = useState<any>(false)
    const [time, setTime] = useState<any>(dayjs().format('HH:mm'))
    const [passengerDropFacility, setPassengerDropFacility] =
        useState<any>(false)
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const [updatedFuelLogs, setUpdatedFuelLogs] = useState<any>([])
    const [parentLocation, setParentLocation] = useState<any>(false)
    const [initialPax, setInitialPax] = useState<any>({ paxOn: 0, paxOff: 0 })
    const [tripEvent, setTripEvent] = useState<any>(false)
    const [fuelTankList, setFuelTankList] = useState<any>(false)
    const [location, setLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })
    const [currentLocation, setCurrentLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })
    const [openNewLocationDialog, setOpenNewLocationDialog] =
        useState<boolean>(false)

    const fuelTankModel = new FuelTankModel()
    const tripEventModel = new TripEventModel()
    const passengerDropFacilityModel =
        new EventType_PassengerDropFacilityModel()
    const geoLocationModel = new GeoLocationModel()
    const tripReportModel = new TripReport_LogBookEntrySectionModel()
    const fuelLogModel = new FuelLogModel()
    const handleTimeChange = (date: any) => {
        setTime(dayjs(date).format('HH:mm'))
    }

    // Memoized handlers to prevent unnecessary re-renders
    const handleTitleChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setPassengerDropFacility((prev: any) => ({
                ...prev,
                title: e.target.value,
            }))
        },
        [],
    )

    const handlePaxOnChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setPassengerDropFacility((prev: any) => ({
                ...prev,
                paxOn: e.target.value,
            }))
        },
        [],
    )

    const handlePaxOffChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setPassengerDropFacility((prev: any) => ({
                ...prev,
                paxOff: e.target.value,
            }))
        },
        [],
    )

    const offlineSelectedEventUseEffect = async () => {
        const event = await tripEventModel.getById(previousDropEvent?.id)
        if (event) {
            setPassengerDropFacility({
                geoLocationID:
                    event.eventType_PassengerDropFacility?.geoLocationID,
                fuelLevel: event.eventType_PassengerDropFacility?.fuelLevel,
                lat: event.eventType_PassengerDropFacility?.lat,
                long: event.eventType_PassengerDropFacility?.long,
                fuelLog: event.eventType_PassengerDropFacility?.fuelLog?.nodes,
            })
            if (
                event.eventType_PassengerDropFacility?.lat &&
                event.eventType_PassengerDropFacility?.long
            ) {
                setCurrentLocation({
                    latitude: event.eventType_PassengerDropFacility?.lat,
                    longitude: event.eventType_PassengerDropFacility?.long,
                })
            }
        }
    }
    useEffect(() => {
        setPassengerDropFacility(false)
        if (selectedEvent) {
            setCurrentEvent(selectedEvent)
            getCurrentEvent(selectedEvent?.id)
        } else {
            if (offline) {
                offlineSelectedEventUseEffect()
            } else {
                if (previousDropEvent?.id > 0) {
                    getPreviousDropEvent({
                        variables: {
                            id: previousDropEvent?.id,
                        },
                    })
                }
            }
        }
    }, [selectedEvent?.id, offline, previousDropEvent?.id])

    useEffect(() => {
        setPassengerDropFacility(false)
        if (currentEvent?.id) {
            getCurrentEvent(currentEvent.id)
        }
    }, [currentEvent?.id])

    const [getPreviousDropEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setPassengerDropFacility({
                    geoLocationID:
                        event.eventType_PassengerDropFacility?.geoLocationID,
                    fuelLevel: event.eventType_PassengerDropFacility?.fuelLevel,
                    lat: event.eventType_PassengerDropFacility?.lat,
                    long: event.eventType_PassengerDropFacility?.long,
                    fuelLog:
                        event.eventType_PassengerDropFacility?.fuelLog?.nodes,
                })
                if (
                    event.eventType_PassengerDropFacility?.lat &&
                    event.eventType_PassengerDropFacility?.long
                ) {
                    setCurrentLocation({
                        latitude: event.eventType_PassengerDropFacility?.lat,
                        longitude: event.eventType_PassengerDropFacility?.long,
                    })
                }
            }
        },
        onError: (error) => {
            console.error('Error getting previous event', error)
        },
    })

    const getCurrentEvent = async (id: any) => {
        if (offline) {
            const event = await tripEventModel.getById(id)
            if (event) {
                if (!event.eventType_PassengerDropFacility) {
                    const eventType_PassengerDropFacility =
                        await passengerDropFacilityModel.getById(id)
                    event.eventType_PassengerDropFacility =
                        eventType_PassengerDropFacility
                }
                setTripEvent(event)
                setPassengerDropFacility({
                    geoLocationID:
                        event.eventType_PassengerDropFacility?.geoLocationID,
                    time: event.eventType_PassengerDropFacility?.time,
                    title: event.eventType_PassengerDropFacility?.title,
                    fuelLevel: event.eventType_PassengerDropFacility?.fuelLevel,
                    paxOn: +(event.eventType_PassengerDropFacility?.paxOn ?? 0),
                    paxOff: +(
                        event.eventType_PassengerDropFacility?.paxOff ?? 0
                    ),
                    type: event.eventType_PassengerDropFacility?.type,
                    lat: event.eventType_PassengerDropFacility?.lat,
                    long: event.eventType_PassengerDropFacility?.long,
                    fuelLog:
                        event.eventType_PassengerDropFacility?.fuelLog?.nodes,
                })
                setInitialPax({
                    paxOn: +(event.eventType_PassengerDropFacility?.paxOn ?? 0),
                    paxOff: +(
                        event.eventType_PassengerDropFacility?.paxOff ?? 0
                    ),
                })
                setTime(event.eventType_PassengerDropFacility?.time ?? null)
                if (
                    event.eventType_PassengerDropFacility?.lat &&
                    event.eventType_PassengerDropFacility?.long
                ) {
                    setCurrentLocation({
                        latitude: event.eventType_PassengerDropFacility?.lat,
                        longitude: event.eventType_PassengerDropFacility?.long,
                    })
                }
            }
        } else {
            getTripEvent({
                variables: {
                    id: id,
                },
            })
        }
    }

    const [getTripEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setTripEvent(event)
                setPassengerDropFacility({
                    geoLocationID:
                        event.eventType_PassengerDropFacility?.geoLocationID,
                    time: event.eventType_PassengerDropFacility?.time,
                    title: event.eventType_PassengerDropFacility?.title,
                    fuelLevel: event.eventType_PassengerDropFacility?.fuelLevel,
                    paxOn: +event.eventType_PassengerDropFacility?.paxOn,
                    paxOff: +event.eventType_PassengerDropFacility?.paxOff,
                    type: event.eventType_PassengerDropFacility?.type,
                    lat: event.eventType_PassengerDropFacility?.lat,
                    long: event.eventType_PassengerDropFacility?.long,
                    fuelLog:
                        event.eventType_PassengerDropFacility?.fuelLog?.nodes,
                })
                setInitialPax({
                    paxOn: +event.eventType_PassengerDropFacility?.paxOn,
                    paxOff: +event.eventType_PassengerDropFacility?.paxOff,
                })
                setTime(event.eventType_PassengerDropFacility?.time)
                if (
                    event.eventType_PassengerDropFacility?.lat &&
                    event.eventType_PassengerDropFacility?.long
                ) {
                    setCurrentLocation({
                        latitude: event.eventType_PassengerDropFacility?.lat,
                        longitude: event.eventType_PassengerDropFacility?.long,
                    })
                }
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    useEffect(() => {
        if (geoLocations) {
            const newLocations = [
                { label: '--- Add new location ---', value: 'newLocation' },
                ...geoLocations
                    .filter((location: any) => location.title)
                    .map((location: any) => ({
                        label: location.title,
                        value: location.id,
                        latitude: location.lat,
                        longitude: location.long,
                    })),
            ]
            setLocations(newLocations)
        }
    }, [geoLocations])

    const handleSave = async () => {
        const variables = {
            input: {
                geoLocationID: passengerDropFacility?.geoLocationID,
                time: time,
                title: passengerDropFacility?.title,
                fuelLevel: passengerDropFacility?.fuelLevel ?? '0',
                paxOn: +passengerDropFacility?.paxOn,
                paxOff: +passengerDropFacility?.paxOff,
                type: type,
                lat: currentLocation.latitude.toString(),
                long: currentLocation.longitude.toString(),
            },
        }

        if (
            initialPax.paxOn !==
                (passengerDropFacility?.paxOn
                    ? +passengerDropFacility.paxOn
                    : 0) ||
            initialPax.paxOff !==
                (passengerDropFacility?.paxOff
                    ? +passengerDropFacility?.paxOff
                    : 0)
        ) {
            const pob =
                (currentTrip?.pob ? +currentTrip.pob : 0) +
                ((passengerDropFacility?.paxOn
                    ? +passengerDropFacility.paxOn
                    : 0) -
                    initialPax.paxOn) -
                ((passengerDropFacility?.paxOff
                    ? +passengerDropFacility?.paxOff
                    : 0) -
                    initialPax.paxOff)
            if (offline) {
                await tripReportModel.save({
                    id: currentTrip.id,
                    pob: +pob,
                })
                updateTripReport({
                    id: tripReport.map((trip: any) => trip.id),
                })
            } else {
                updateTripLogPax({
                    variables: {
                        input: {
                            id: currentTrip.id,
                            pob: +pob,
                        },
                    },
                })
            }
        }

        if (currentEvent) {
            if (offline) {
                // updateTripEvent
                await tripEventModel.save({
                    id: +currentEvent.id,
                    eventCategory: 'PassengerDropFacility',
                    eventType_PassengerDropFacilityID: +currentEvent.id,
                    logBookEntrySectionID: currentTrip.id,
                })
                getCurrentEvent(currentEvent?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
                // updateEventType_PassengerDropFacility
                await passengerDropFacilityModel.save({
                    id: +selectedEvent?.eventType_PassengerDropFacilityID,
                    ...variables.input,
                })
                // updateFuelLogs
                const currentID =
                    +selectedEvent?.eventType_PassengerDropFacilityID
                Promise.all(
                    fuelTankList?.map(async (fuelTank: any) => {
                        await fuelTankModel.save({
                            id: fuelTank.id,
                            currentLevel: fuelTank.currentLevel,
                        })
                        if (
                            currentEvent &&
                            currentEvent?.fuelLogs?.nodes.find(
                                (log: any) => log.fuelTankID === fuelTank.id,
                            ).id > 0
                        ) {
                            await fuelLogModel.save({
                                id: currentEvent?.fuelLogs?.nodes.find(
                                    (log: any) =>
                                        log.fuelTankID === fuelTank.id,
                                ).id,
                                fuelTankID: fuelTank.id,
                                fuelAfter: fuelTank.currentLevel,
                                date: dayjs().format('YYYY-MM-DD'),
                                eventType_PassengerDropFacilityID: currentID,
                            })
                        } else {
                            await fuelLogModel.save({
                                id: generateUniqueId(),
                                fuelTankID: fuelTank.id,
                                fuelAfter: fuelTank.currentLevel,
                                date: dayjs().format('YYYY-MM-DD'),
                                eventType_PassengerDropFacilityID: currentID,
                            })
                        }
                    }),
                )
            } else {
                updateTripEvent({
                    variables: {
                        input: {
                            id: +currentEvent.id,
                            eventCategory: 'PassengerDropFacility',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
                updateEventType_PassengerDropFacility({
                    variables: {
                        input: {
                            id: +selectedEvent?.eventType_PassengerDropFacilityID,
                            ...variables.input,
                        },
                    },
                })
                updateFuelLogs(
                    +selectedEvent?.eventType_PassengerDropFacilityID,
                )
            }
        } else {
            if (offline) {
                const id = generateUniqueId()
                const tripEventData = await tripEventModel.save({
                    id: id,
                    eventCategory: 'PassengerDropFacility',
                    eventType_PassengerDropFacilityID: id,
                    logBookEntrySectionID: currentTrip.id,
                })
                setCurrentEvent(tripEventData)
                const passengerDropFacilityData =
                    await passengerDropFacilityModel.save({
                        id: generateUniqueId(),
                        geoLocationID: passengerDropFacility?.geoLocationID,
                        time: time,
                        title: passengerDropFacility?.title,
                        fuelLevel: passengerDropFacility?.fuelLevel ?? '0',
                        paxOn: +passengerDropFacility?.paxOn,
                        paxOff: +passengerDropFacility?.paxOff,
                        type: type,
                        lat: currentLocation.latitude.toString(),
                        long: currentLocation.longitude.toString(),
                    })
                const currentID = passengerDropFacilityData.id
                if (fuelTankList) {
                    Promise.all(
                        fuelTankList?.map(async (fuelTank: any) => {
                            await fuelTankModel.save({
                                id: fuelTank.id,
                                currentLevel: fuelTank.currentLevel,
                            })
                            if (
                                tripEventData &&
                                tripEventData?.fuelLogs?.nodes.find(
                                    (log: any) =>
                                        log.fuelTankID === fuelTank.id,
                                ).id > 0
                            ) {
                                await fuelLogModel.save({
                                    id: tripEventData?.fuelLogs?.nodes.find(
                                        (log: any) =>
                                            log.fuelTankID === fuelTank.id,
                                    ).id,
                                    fuelTankID: fuelTank.id,
                                    fuelAfter: fuelTank.currentLevel,
                                    date: dayjs().format('YYYY-MM-DD'),
                                    eventType_PassengerDropFacilityID:
                                        currentID,
                                })
                            } else {
                                await fuelLogModel.save({
                                    id: generateUniqueId(),
                                    fuelTankID: fuelTank.id,
                                    fuelAfter: fuelTank.currentLevel,
                                    date: dayjs().format('YYYY-MM-DD'),
                                    eventType_PassengerDropFacilityID:
                                        currentID,
                                })
                            }
                        }),
                    )
                }

                await tripEventModel.save({
                    id: tripEventData?.id,
                    eventType_PassengerDropFacilityID:
                        passengerDropFacilityData.id,
                })
                getCurrentEvent(tripEventData?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
                closeModal()
            } else {
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'PassengerDropFacility',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
        }
    }

    const [updateTripLogPax] = useMutation(
        UpdateTripReport_LogBookEntrySection,
        {
            onCompleted: (data) => {
                updateTripReport({
                    id: tripReport.map((trip: any) => trip.id),
                })
            },
            onError: (error) => {
                console.error('onError', error)
            },
        },
    )

    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            const data = response.createTripEvent
            setCurrentEvent(data)
            createEventType_PassengerDropFacility({
                variables: {
                    input: {
                        geoLocationID: passengerDropFacility?.geoLocationID,
                        time: time,
                        title: passengerDropFacility?.title,
                        fuelLevel: passengerDropFacility?.fuelLevel ?? '0',
                        paxOn: +passengerDropFacility?.paxOn,
                        paxOff: +passengerDropFacility?.paxOff,
                        type: type,
                        lat: currentLocation.latitude.toString(),
                        long: currentLocation.longitude.toString(),
                    },
                },
            })
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
        },
    })

    const [createEventType_PassengerDropFacility] = useMutation(
        CreateEventType_PassengerDropFacility,
        {
            onCompleted: (response) => {
                const data = response.createEventType_PassengerDropFacility
                updateFuelLogs(data.id)
                updateTripEvent({
                    variables: {
                        input: {
                            id: currentEvent?.id,
                            eventType_PassengerDropFacilityID: data.id,
                        },
                    },
                })
                closeModal()
            },
            onError: (error) => {
                console.error('Error creating passenger drop facility', error)
            },
        },
    )

    const [updateEventType_PassengerDropFacility] = useMutation(
        UpdateEventType_PassengerDropFacility,
        {
            onCompleted: (response) => {
                const data = response.updateEventType_PassengerDropFacility
            },
            onError: (error) => {
                console.error('Error updating passenger drop facility', error)
            },
        },
    )

    const [updateTripEvent] = useMutation(UpdateTripEvent, {
        onCompleted: (response) => {
            getCurrentEvent(currentEvent?.id)
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
        },
        onError: (error) => {
            console.error('Error updating trip event', error)
        },
    })

    const displayField = (fieldName: string) => {
        const eventTypesConfig =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'EventType_LogBookComponent',
            )
        if (
            eventTypesConfig?.length > 0 &&
            eventTypesConfig[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const handleLocationChange = (value: any) => {
        // If value is null or undefined, clear the location
        if (!value) {
            setPassengerDropFacility({
                ...passengerDropFacility,
                geoLocationID: 0,
                lat: null,
                long: null,
            })
            return
        }

        // Handle "Add new location" option
        if (value.value === 'newLocation') {
            toast('Getting location', {
                description: 'Getting your current location...',
            })
            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(({ coords }) => {
                    const { latitude, longitude } = coords
                    setLocation({ latitude, longitude })
                    // toast.success('Success', {
                    //     description: 'Location found',
                    // })
                    setOpenNewLocationDialog(true)
                })
            } else {
                toast.error('Error', {
                    description: 'Geolocation is not supported by your browser',
                })
                setOpenNewLocationDialog(true)
            }
            return
        }

        // Check if the value is from dropdown selection (has 'value' property)
        if (value.value) {
            // Handle location selected from dropdown
            setPassengerDropFacility({
                ...passengerDropFacility,
                geoLocationID: +value.value,
                lat: null,
                long: null,
            })

            // If the value object has latitude and longitude, update currentLocation
            if (value.latitude !== undefined && value.longitude !== undefined) {
                setCurrentLocation({
                    latitude: value.latitude,
                    longitude: value.longitude,
                })
            }
        } else if (
            value.latitude !== undefined &&
            value.longitude !== undefined
        ) {
            // Handle direct coordinates input
            setPassengerDropFacility({
                ...passengerDropFacility,
                geoLocationID: 0, // Reset geoLocationID when using direct coordinates
                lat: value.latitude,
                long: value.longitude,
            })

            // Update currentLocation
            setCurrentLocation({
                latitude: value.latitude,
                longitude: value.longitude,
            })
        }
    }

    const handleCreateNewLocation = async () => {
        const title = document.getElementById(
            'new-location-title',
        ) as HTMLInputElement
        const latitude = document.getElementById(
            'new-location-latitude',
        ) as HTMLInputElement
        const longitude = document.getElementById(
            'new-location-longitude',
        ) as HTMLInputElement
        if (title && latitude && longitude) {
            if (offline) {
                const data = await geoLocationModel.save({
                    id: generateUniqueId(),
                    title: title.value,
                    lat: +latitude.value,
                    long: +longitude.value,
                    parentID: parentLocation,
                })
                setLocations([
                    ...locations,
                    {
                        label: data.title,
                        value: data.id,
                        latitude: data.lat,
                        longitude: data.long,
                    },
                ])
                setPassengerDropFacility({
                    ...passengerDropFacility,
                    geoLocationID: data.id,
                })
                setOpenNewLocationDialog(false)
            } else {
                createGeoLocation({
                    variables: {
                        input: {
                            title: title.value,
                            lat: +latitude.value,
                            long: +longitude.value,
                            parentID: parentLocation,
                        },
                    },
                })
            }
        }
    }

    const [createGeoLocation] = useMutation(CREATE_GEO_LOCATION, {
        onCompleted: (response) => {
            const data = response.createGeoLocation
            setLocations([
                ...locations,
                {
                    label: data.title,
                    value: data.id,
                    latitude: data.lat,
                    longitude: data.long,
                },
            ])
            setPassengerDropFacility({
                ...passengerDropFacility,
                geoLocationID: data.id,
            })
            setOpenNewLocationDialog(false)
        },
        onError: (error) => {
            toast.error('Error', {
                description: 'Error creating GeoLocation: ' + error.message,
            })
            setOpenNewLocationDialog(false)
            console.error('Error creating new location', error)
        },
    })

    const handleParentLocationChange = (selectedLocation: any) => {
        setParentLocation(selectedLocation.value)
    }

    const [queryGetFuelTanks] = useLazyQuery(GET_FUELTANKS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readFuelTanks.nodes
            // Initialize currentLevel for each tank if not already set
            const initializedData = data.map((tank: any) => ({
                ...tank,
                currentLevel: tank.currentLevel ?? getInitialFuelLevel(tank),
            }))
            setFuelTankList(initializedData)
        },
        onError: (error: any) => {
            console.error('getFuelTanks error', error)
        },
    })

    const getFuelTanks = async (fuelTankIds: any) => {
        if (offline) {
            const data = await fuelTankModel.getByIds(fuelTankIds)
            // Initialize currentLevel for each tank if not already set
            const initializedData = data.map((tank: any) => ({
                ...tank,
                currentLevel: tank.currentLevel ?? getInitialFuelLevel(tank),
            }))
            setFuelTankList(initializedData)
        } else {
            await queryGetFuelTanks({
                variables: {
                    id: fuelTankIds,
                },
            })
        }
    }

    const handleSetVessel = (vessel: any) => {
        const fuelTankIds = vessel?.parentComponent_Components?.nodes
            .filter(
                (item: any) =>
                    item.basicComponent.componentCategory === 'FuelTank',
            )
            .map((item: any) => {
                return item.basicComponent.id
            })
        fuelTankIds.length > 0 && getFuelTanks(fuelTankIds)
    }

    useEffect(() => {
        if (vessel) {
            handleSetVessel(vessel)
        }
    }, [vessel])

    const handleUpdateFuelTank = useCallback(
        (tank: any, value: any) => {
            if (tank.capacity < +value) {
                toast.error('Error', {
                    description:
                        'Fuel level cannot be higher than tank capacity of ' +
                        tank.capacity,
                })
                return
            }
            setFuelTankList((prevList: any) =>
                prevList.map((item: any) => {
                    if (item.id === tank.id) {
                        return { ...item, currentLevel: +value }
                    }
                    return item
                }),
            )
        },
        [toast],
    )

    const [updateFuelLog] = useMutation(UPDATE_FUELLOG, {
        onCompleted: (response) => {
            const data = response.updateFuelLog
        },
        onError: (error) => {
            console.error('Error updating fuel log', error)
        },
    })

    const [createFuelLog] = useMutation(CREATE_FUELLOG, {
        onCompleted: (response) => {
            const data = response.createFuelLog
        },
        onError: (error) => {
            console.error('Error creating fuel log', error)
        },
    })

    const [updateFuelTank] = useMutation(UpdateFuelTank, {
        onCompleted: (response) => {
            const data = response.updateFuelTank
            const fuelLog = updatedFuelLogs
                .filter((log: any) => log.fuelTank.id === data.id)
                .sort(
                    (a: any, b: any) =>
                        new Date(a.date).getTime() - new Date(b.date).getTime(),
                )[0]
            if (fuelLog) {
                updateFuelLog({
                    variables: {
                        input: {
                            id: fuelLog.id,
                            fuelAfter: +fuelLog.fuelAfter,
                        },
                    },
                })
            }
        },
        onError: (error) => {
            console.error('Error updating fuel tank', error)
        },
    })

    const updateFuelLogs = (currentID: number = 0) => {
        fuelTankList?.map((fuelTank: any) => {
            const variables = {
                input: {
                    id: fuelTank.id,
                    currentLevel: fuelTank.currentLevel,
                },
            }
            updateFuelTank({
                variables: variables,
            })
            currentEvent &&
            currentEvent?.fuelLogs?.nodes.find(
                (log: any) => log.fuelTankID === fuelTank.id,
            ).id > 0
                ? updateFuelLog({
                      variables: {
                          input: {
                              id: currentEvent?.fuelLogs?.nodes.find(
                                  (log: any) => log.fuelTankID === fuelTank.id,
                              ).id,
                              fuelTankID: fuelTank.id,
                              fuelAfter: fuelTank.currentLevel,
                              date: dayjs().format('YYYY-MM-DD'),
                              eventType_PassengerDropFacilityID: currentID,
                          },
                      },
                  })
                : createFuelLog({
                      variables: {
                          input: {
                              fuelTankID: fuelTank.id,
                              fuelAfter: fuelTank.currentLevel,
                              date: dayjs().format('YYYY-MM-DD'),
                              eventType_PassengerDropFacilityID: currentID,
                          },
                      },
                  })
        })
    }

    const getInitialFuelLevel = useCallback(
        (tank: any) => {
            if (tripReport.length > 0) {
                const fuelLogs = tripReport
                    .map((trip: any) => {
                        return trip.tripEvents.nodes
                            .filter(
                                (event: any) =>
                                    (event.eventCategory === 'Tasking' &&
                                        event.eventType_Tasking.fuelLog.nodes
                                            .length > 0) ||
                                    (event.eventCategory ===
                                        'RefuellingBunkering' &&
                                        event.eventType_RefuellingBunkering
                                            .fuelLog.nodes.length > 0) ||
                                    (event.eventCategory ===
                                        'PassengerDropFacility' &&
                                        event.eventType_PassengerDropFacility
                                            .fuelLog.nodes.length > 0),
                            )
                            .flatMap(
                                (event: any) =>
                                    (event.eventCategory === 'Tasking' &&
                                        event.eventType_Tasking.fuelLog
                                            .nodes) ||
                                    (event.eventCategory ===
                                        'RefuellingBunkering' &&
                                        event.eventType_RefuellingBunkering
                                            .fuelLog.nodes) ||
                                    (event.eventCategory ===
                                        'PassengerDropFacility' &&
                                        event.eventType_PassengerDropFacility
                                            .fuelLog.nodes),
                            )
                    })
                    .flat()
                const lastFuelLog = fuelLogs
                    ?.filter((log: any) => log.fuelTank.id === tank.id)
                    .sort((a: any, b: any) => b.id - a.id)?.[0]
                if (lastFuelLog) {
                    return lastFuelLog.fuelAfter
                }
            }
            const fuelLog = updatedFuelLogs
                .filter((log: any) => log.fuelTank.id === tank.id)
                .sort(
                    (a: any, b: any) =>
                        new Date(a.date).getTime() - new Date(b.date).getTime(),
                )[0]
            return fuelLog
                ? +tank.capacity > +fuelLog.fuelAfter
                    ? +fuelLog.fuelAfter
                    : +tank.capacity
                : +tank.currentLevel
        },
        [tripReport, updatedFuelLogs],
    )

    const getFuelLogs = async (fuelLogIds: any) => {
        if (offline) {
            const data = await fuelTankModel.getByIds(fuelLogIds)
        } else {
            await queryGetFuelLogs({
                variables: {
                    id: fuelLogIds,
                },
            })
        }
    }

    const [queryGetFuelLogs] = useLazyQuery(GET_FUELLOGS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readFuelLogs.nodes
            setUpdatedFuelLogs(data)
        },
        onError: (error: any) => {
            console.error('getFuelLogs error', error)
        },
    })

    useEffect(() => {
        getFuelLogs(fuelLogs.map((item: any) => item.id))
    }, [])

    return (
        <div
            key={`passenger-drop-facility-${currentEvent?.id || 'new'}`}
            className={`${locked ? 'pointer-events-none' : ''}`}
            style={{ scrollBehavior: 'auto' }}>
            {!inLogbook && (
                <h3 className="text-lg font-semibold mb-4">
                    {type === 'PassengerArrival' && 'Arrival activity details'}
                    {type === 'PassengerDeparture' &&
                        'Departure activity details'}
                    {type === 'WaterTaxiService' &&
                        'Water taxi service details'}
                    {type === 'ScheduledPassengerService' &&
                        'Scheduled passenger service details'}
                </h3>
            )}
            <div className="space-y-6">
                <Label label="Details" htmlFor="title">
                    <Input
                        key="passenger-drop-title"
                        id="title"
                        name="title"
                        type="text"
                        value={passengerDropFacility?.title ?? ''}
                        placeholder="Details of arrival"
                        onChange={handleTitleChange}
                    />
                </Label>

                <Label label="Time of arrival">
                    <TimeField
                        time={time}
                        handleTimeChange={handleTimeChange}
                        timeID="time"
                        fieldName="Time"
                    />
                </Label>
                <Label label="Location">
                    <LocationField
                        offline={offline}
                        setCurrentLocation={setCurrentLocation}
                        handleLocationChange={handleLocationChange}
                        currentEvent={tripEvent.eventType_PassengerDropFacility}
                    />
                </Label>
                {displayField(type + '_FuelLevel') && (
                    <>
                        {fuelTankList &&
                            fuelTankList.map((tank: any) => {
                                const fuelTankChangeHandler = (e: any) =>
                                    handleUpdateFuelTank(tank, e.target.value)

                                return (
                                    <Label
                                        key={`fuel-tank-${tank.id}`}
                                        label={tank.title}
                                        htmlFor={`fuel-tank-${tank.id}`}
                                        className="flex items-center gap-3">
                                        <SealogsFuelIcon className="w-6 h-6" />
                                        <Input
                                            key={`fuel-input-${tank.id}`}
                                            id={`fuel-tank-${tank.id}`}
                                            type="number"
                                            placeholder="Fuel end"
                                            value={
                                                tank.currentLevel ??
                                                getInitialFuelLevel(tank)
                                            }
                                            min={0}
                                            max={
                                                passengerDropFacility?.fuelLog
                                                    ? passengerDropFacility.fuelLog.find(
                                                          (log: any) =>
                                                              log.fuelTank
                                                                  .id ===
                                                              tank.id,
                                                      )?.fuelAfter
                                                    : tank.capacity
                                            }
                                            onChange={fuelTankChangeHandler}
                                        />
                                    </Label>
                                )
                            })}
                    </>
                )}
                {displayField(type + '_Pax') && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Label label="Passengers boarding" htmlFor="paxOn">
                            <Input
                                key="passenger-drop-pax-on"
                                id="paxOn"
                                name="paxOn"
                                type="number"
                                value={
                                    isNaN(passengerDropFacility?.paxOn)
                                        ? 0
                                        : +passengerDropFacility?.paxOn
                                }
                                placeholder="Passengers getting on"
                                min="0"
                                onChange={handlePaxOnChange}
                            />
                        </Label>
                        <Label label="Passengers embarking" htmlFor="paxOff">
                            <Input
                                key="passenger-drop-pax-off"
                                id="paxOff"
                                name="paxOff"
                                type="number"
                                value={
                                    isNaN(passengerDropFacility?.paxOff)
                                        ? 0
                                        : +passengerDropFacility?.paxOff
                                }
                                placeholder="Passengers getting off"
                                min="0"
                                onChange={handlePaxOffChange}
                            />
                        </Label>
                    </div>
                )}

                <div className="flex justify-end">
                    <Button
                        variant="back"
                        iconLeft={ArrowLeft}
                        onClick={() => closeModal()}>
                        Cancel
                    </Button>
                    <Button
                        variant="primary"
                        iconLeft={Check}
                        onClick={locked ? () => {} : handleSave}
                        disabled={locked}>
                        {selectedEvent ? 'Update' : 'Save'}
                    </Button>
                </div>
            </div>

            <AlertDialogNew
                openDialog={openNewLocationDialog}
                setOpenDialog={setOpenNewLocationDialog}
                actionText="Add New Location"
                title="Add New Location"
                handleCreate={handleCreateNewLocation}>
                <Label
                    label="Location Title"
                    htmlFor="new-location-title"
                    className="my-4">
                    <Input
                        id="new-location-title"
                        type="text"
                        required
                        placeholder="Location Title"
                    />
                </Label>
                <Label label="Parent Location (Optional)" className="mb-4">
                    <Combobox
                        options={locations}
                        onChange={(value) => handleParentLocationChange(value)}
                        placeholder="Parent Location (Optional)"
                        buttonClassName="w-full"
                    />
                </Label>
                <Label
                    label="Latitude"
                    htmlFor="new-location-latitude"
                    className="mb-4">
                    <Input
                        id="new-location-latitude"
                        type="text"
                        defaultValue={location.latitude}
                        required
                        placeholder="Latitude"
                    />
                </Label>
                <Label label="Longitude" htmlFor="new-location-longitude">
                    <Input
                        id="new-location-longitude"
                        type="text"
                        defaultValue={location.longitude}
                        required
                        placeholder="Longitude"
                    />
                </Label>
            </AlertDialogNew>
        </div>
    )
}
